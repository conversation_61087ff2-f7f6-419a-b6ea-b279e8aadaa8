<?php
session_start();
include '../db_connect.php';

if (!isset($_SESSION['username'])) {
    header('Location: ../login.php');
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_SESSION['username'];
    
    // Create upload directory if it doesn't exist
    $upload_dir = '../uploads/';
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }

    // Handle file uploads
    $profile_picture_path = null;
    $company_logo_path = null;
    
    // Process profile picture upload
    if (isset($_FILES['profile_picture']) && $_FILES['profile_picture']['error'] === UPLOAD_ERR_OK) {
        $file_info = pathinfo($_FILES['profile_picture']['name']);
        $file_extension = strtolower($file_info['extension']);
        
        // Validate file type
        $allowed_types = ['jpg', 'jpeg', 'png', 'gif'];
        if (in_array($file_extension, $allowed_types)) {
            $new_filename = 'profile_' . $username . '_' . time() . '.' . $file_extension;
            $upload_path = $upload_dir . $new_filename;
            
            if (move_uploaded_file($_FILES['profile_picture']['tmp_name'], $upload_path)) {
                $profile_picture_path = 'uploads/' . $new_filename;
            }
        }
    }
    
    // Process company logo upload
    if (isset($_FILES['company_logo']) && $_FILES['company_logo']['error'] === UPLOAD_ERR_OK) {
        $file_info = pathinfo($_FILES['company_logo']['name']);
        $file_extension = strtolower($file_info['extension']);
        
        // Validate file type
        $allowed_types = ['jpg', 'jpeg', 'png', 'gif'];
        if (in_array($file_extension, $allowed_types)) {
            $new_filename = 'logo_' . $username . '_' . time() . '.' . $file_extension;
            $upload_path = $upload_dir . $new_filename;
            
            if (move_uploaded_file($_FILES['company_logo']['tmp_name'], $upload_path)) {
                $company_logo_path = 'uploads/' . $new_filename;
            }
        }
    }
    
    // List of fields to update
    $fields = [
        'owner_name', 'franchisee_name', 'address_line1', 'address_line2', 'address_line3',
        'city', 'pincode', 'zone', 'phone', 'mobile', 'business_email',
        'gst_no', 'pan_no', 'state_code', 'account_name', 'bank_name',
        'branch', 'account_no', 'account_type', 'ifsc_code', 'sac_code', 'website'
    ];
    
    // Build the SQL query
    $sql_parts = [];
    $types = "";
    $values = [];
    
    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            $sql_parts[] = "`$field` = ?";
            $types .= "s";
            $values[] = $_POST[$field];
        }
    }
    
    // Add file paths if files were uploaded
    if ($profile_picture_path !== null) {
        $sql_parts[] = "profile_picture = ?";
        $types .= "s";
        $values[] = $profile_picture_path;
    }
    
    if ($company_logo_path !== null) {
        $sql_parts[] = "company_logo = ?";
        $types .= "s";
        $values[] = $company_logo_path;
    }
    
    // Add profile_updated timestamp
    $sql_parts[] = "profile_updated = CURRENT_TIMESTAMP";
    
    if (!empty($sql_parts)) {
        $sql = "UPDATE users SET " . implode(", ", $sql_parts) . " WHERE username = ?";
        $types .= "s";
        $values[] = $username;
        
        $stmt = $conn->prepare($sql);
        
        if ($stmt) {
            // Bind parameters dynamically
            $bind_params = array($types);
            for ($i = 0; $i < count($values); $i++) {
                $bind_params[] = &$values[$i];
            }
            call_user_func_array(array($stmt, 'bind_param'), $bind_params);
            
            if ($stmt->execute()) {
                $_SESSION['profile_message'] = "Profile updated successfully!";
            } else {
                $_SESSION['profile_message'] = "Error updating profile: " . $stmt->error;
            }
            $stmt->close();
        } else {
            $_SESSION['profile_message'] = "Error preparing statement: " . $conn->error;
        }
    }
}

// Redirect back to profile page
header('Location: ../index.php?page=profile');
exit(); 