<?php
session_start();
include 'db_connect.php'; // Database connection

if (!isset($_SESSION['username'])) {
    die("<script>alert('Error: You must be logged in.'); window.location.href='login.php';</script>");
}

$username = $_SESSION['username'];

if (!isset($_GET['id']) || empty($_GET['id'])) {
    die("<script>alert('Error: Invalid customer ID.'); window.location.href='dashboard.php?page=customer_dashboard';</script>");
}

$id = $_GET['id'];

$check_stmt = $conn->prepare("SELECT id FROM customers WHERE id = ? AND username = ?");
$check_stmt->bind_param("is", $id, $username);
$check_stmt->execute();
$check_stmt->store_result();

if ($check_stmt->num_rows === 0) {
    die("<script>alert('Error: Customer not found or access denied.'); window.location.href='index.php?page=customer_dashboard';</script>");
}
$check_stmt->close();

$stmt = $conn->prepare("DELETE FROM customers WHERE id = ? AND username = ?");
$stmt->bind_param("is", $id, $username);

if ($stmt->execute()) {
    echo "<script>alert('✅ Customer successfully deleted!'); window.location.href = 'index.php?page=customer_dashboard';</script>";
} else {
    die("<script>alert('Error: Could not delete customer.'); window.history.back();</script>");
}

$stmt->close();
$conn->close();
?>
