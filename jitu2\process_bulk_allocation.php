<?php
session_start();
require 'db_connect.php';
require 'vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\IOFactory;

if (!isset($_SESSION['username'])) {
    $error_message = 'Error: You must be logged in.';
    echo "<script>alert(" . json_encode($error_message) . "); window.location.href='login.php';</script>";
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_FILES['excel_file'])) {
    $error_message = 'Invalid request.';
    echo "<script>alert(" . json_encode($error_message) . "); window.location.href='dashboard.php?page=cn-entry';</script>";
    exit();
}

$username = $_SESSION['username'];
$uploadedFile = $_FILES['excel_file'];

// Validate file
$allowedTypes = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel'
];

if (!in_array($uploadedFile['type'], $allowedTypes)) {
    $error_message = 'Invalid file type. Please upload an Excel file.';
    echo "<script>alert(" . json_encode($error_message) . "); window.location.href='index.php?page=cn-entry';</script>";
    exit();
}

try {
    $spreadsheet = IOFactory::load($uploadedFile['tmp_name']);
    $worksheet = $spreadsheet->getActiveSheet();
    $rows = $worksheet->toArray();

    // Skip header row and validate headers
    $headers = array_shift($rows);
    $expectedHeaders = ['CN No.', 'Customer'];
    if (array_diff($expectedHeaders, $headers)) {
        throw new Exception('Invalid Excel format. Please use the provided template.');
    }

    // Get list of valid customers
    $stmt = $conn->prepare("SELECT `short_name` FROM `customers`");
    $stmt->execute();
    $result = $stmt->get_result();
    $validCustomers = [];
    while ($row = $result->fetch_assoc()) {
        $validCustomers[] = $row['short_name'];
    }
    $stmt->close();

    // Database columns: id, cn_number, cn_date, cn_expiry_date, cn_type, username, customer, allocation_date
    $stmt = $conn->prepare("
        UPDATE `cn_entries` 
        SET `customer` = ?,
            `allocation_date` = CURRENT_DATE()
        WHERE `cn_number` = ? 
        AND `username` = ? 
        AND `customer` IS NULL
    ");

    $successCount = 0;
    $errors = [];

    foreach ($rows as $index => $row) {
        if (empty($row[0])) continue; // Skip empty rows

        $cn_number = trim($row[0]);
        $customer = trim($row[1]);

        // Validate data
        if (empty($cn_number) || empty($customer)) {
            $errors[] = "Row " . ($index + 2) . ": Missing required data";
            continue;
        }

        if (!in_array($customer, $validCustomers)) {
            $errors[] = "Row " . ($index + 2) . ": Invalid Customer name";
            continue;
        }

        // First check if CN exists and its current status
        $checkStmt = $conn->prepare("
            SELECT `cn_number`, `customer`, `username`
            FROM `cn_entries` 
            WHERE `cn_number` = ?
        ");
        $checkStmt->bind_param("s", $cn_number);
        $checkStmt->execute();
        $checkResult = $checkStmt->get_result();
        
        if ($checkResult->num_rows === 0) {
            $errors[] = "Row " . ($index + 2) . ": CN number $cn_number does not exist in the system";
            continue;
        }
        
        $cnData = $checkResult->fetch_assoc();
        
        // Check if username matches
        if ($cnData['username'] !== $username) {
            $errors[] = "Row " . ($index + 2) . ": CN number $cn_number exists but belongs to user " . $cnData['username'];
            continue;
        }
        
        if (!empty($cnData['customer'])) {
            $errors[] = "Row " . ($index + 2) . ": CN number $cn_number is already allocated to " . $cnData['customer'];
            continue;
        }
        $checkStmt->close();

        // Update data if CN exists and is not allocated
        $stmt->bind_param("sss", 
            $customer,     // customer
            $cn_number,    // cn_number (WHERE)
            $username      // username (WHERE)
        );
        
        if ($stmt->execute()) {
            if ($stmt->affected_rows > 0) {
                $successCount++;
            } else {
                $errors[] = "Row " . ($index + 2) . ": Failed to update CN number $cn_number";
            }
        } else {
            $errors[] = "Row " . ($index + 2) . ": " . $stmt->error;
        }
    }

    $stmt->close();
    $conn->close();

    // Prepare result message
    $message = "Successfully allocated $successCount CN entries.";
    if (!empty($errors)) {
        $message .= "\n\nErrors:\n" . implode("\n", $errors);
    }

    echo "<script>alert(" . json_encode($message) . "); window.location.href='index.php?page=cn-entry';</script>";

} catch (Exception $e) {
    $error_message = 'Error processing file: ' . $e->getMessage();
    echo "<script>alert(" . json_encode($error_message) . "); window.location.href='index.php?page=cn-entry';</script>";
    exit();
}
?> 