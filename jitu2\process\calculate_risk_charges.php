<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include database connection
include '../db_connect.php';

// Check if required parameters are provided
if (!isset($_POST['customer']) || !isset($_POST['waybill_value']) || !isset($_POST['risk_type'])) {
    echo json_encode([
        'success' => false,
        'error' => 'Missing required parameters'
    ]);
    exit;
}

$customer = trim($_POST['customer']);
$waybill_value = floatval($_POST['waybill_value']);
$risk_type = trim($_POST['risk_type']);

// Debug logging
error_log("Processing risk charges calculation for customer: $customer, waybill: $waybill_value, risk type: $risk_type");

// Validate inputs
if (empty($customer) || $waybill_value <= 0) {
    echo json_encode([
        'success' => false,
        'error' => 'Invalid input values'
    ]);
    exit;
}

// Get customer's risk rates from the database
$query = "SELECT owner_risk, carrier_risk FROM customers WHERE short_name = ? AND username = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("ss", $customer, $_SESSION['username']);
$stmt->execute();
$result = $stmt->get_result();

if ($row = $result->fetch_assoc()) {
    // Calculate risk charges based on risk type
    $risk_rate = $risk_type === 'Owner Risk' ? $row['owner_risk'] : $row['carrier_risk'];
    $risk_value = ($waybill_value * $risk_rate / 100);
    
    // Debug logging
    error_log("Risk calculation: waybill=$waybill_value, rate=$risk_rate, result=$risk_value");
    
    echo json_encode([
        'success' => true,
        'risk_value' => number_format($risk_value, 2, '.', '')
    ]);
} else {
    // Debug logging
    error_log("Customer not found: $customer for user: " . $_SESSION['username']);
    
    echo json_encode([
        'success' => false,
        'error' => 'Customer not found'
    ]);
}

// Close statement and connection
$stmt->close();
$conn->close();
?> 