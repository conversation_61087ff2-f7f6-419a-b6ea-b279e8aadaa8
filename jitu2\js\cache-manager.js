/**
 * ALM Application Cache Manager
 * Centralized cache management for localStorage
 */

class CacheManager {
    constructor() {
        this.cacheKeys = {
            CASH_ENTRY: 'alm_cash_entry_data',
            CREDIT_ENTRY: 'alm_credit_entry_data',
            SETTINGS: 'alm_settings_data',
            USER_PREFERENCES: 'alm_user_preferences'
        };
        this.defaultDuration = 5 * 60 * 1000; // 5 minutes
    }

    /**
     * Check if cached data is valid
     */
    isCacheValid(cacheData, customDuration = null) {
        if (!cacheData || !cacheData.timestamp) return false;
        const now = new Date().getTime();
        const duration = customDuration || this.defaultDuration;
        return (now - cacheData.timestamp) < duration;
    }

    /**
     * Get data from cache
     */
    get(key, customDuration = null) {
        try {
            const cached = localStorage.getItem(key);
            if (cached) {
                const cacheData = JSON.parse(cached);
                if (this.isCacheValid(cacheData, customDuration)) {
                    return cacheData.data;
                } else {
                    // Remove expired cache
                    this.remove(key);
                }
            }
        } catch (error) {
            console.error('Error reading cache:', error);
            this.remove(key);
        }
        return null;
    }

    /**
     * Save data to cache
     */
    set(key, data, customDuration = null) {
        try {
            const cacheData = {
                data: data,
                timestamp: new Date().getTime(),
                duration: customDuration || this.defaultDuration
            };
            localStorage.setItem(key, JSON.stringify(cacheData));
            return true;
        } catch (error) {
            console.error('Error saving to cache:', error);
            return false;
        }
    }

    /**
     * Remove specific cache entry
     */
    remove(key) {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (error) {
            console.error('Error removing cache:', error);
            return false;
        }
    }

    /**
     * Clear all ALM application caches
     */
    clearAll() {
        try {
            Object.values(this.cacheKeys).forEach(key => {
                localStorage.removeItem(key);
            });
            return true;
        } catch (error) {
            console.error('Error clearing all caches:', error);
            return false;
        }
    }

    /**
     * Get cache statistics
     */
    getStats() {
        const stats = {
            totalCaches: 0,
            validCaches: 0,
            expiredCaches: 0,
            totalSize: 0,
            cacheDetails: {}
        };

        Object.entries(this.cacheKeys).forEach(([name, key]) => {
            try {
                const cached = localStorage.getItem(key);
                if (cached) {
                    stats.totalCaches++;
                    stats.totalSize += cached.length;
                    
                    const cacheData = JSON.parse(cached);
                    const isValid = this.isCacheValid(cacheData);
                    
                    if (isValid) {
                        stats.validCaches++;
                    } else {
                        stats.expiredCaches++;
                    }
                    
                    stats.cacheDetails[name] = {
                        key: key,
                        size: cached.length,
                        timestamp: cacheData.timestamp,
                        isValid: isValid,
                        age: new Date().getTime() - cacheData.timestamp
                    };
                }
            } catch (error) {
                console.error(`Error reading cache stats for ${key}:`, error);
            }
        });

        return stats;
    }

    /**
     * Clean expired caches
     */
    cleanExpired() {
        let cleaned = 0;
        Object.values(this.cacheKeys).forEach(key => {
            try {
                const cached = localStorage.getItem(key);
                if (cached) {
                    const cacheData = JSON.parse(cached);
                    if (!this.isCacheValid(cacheData)) {
                        localStorage.removeItem(key);
                        cleaned++;
                    }
                }
            } catch (error) {
                console.error(`Error cleaning cache ${key}:`, error);
                localStorage.removeItem(key);
                cleaned++;
            }
        });
        return cleaned;
    }

    /**
     * Force refresh cache for specific key
     */
    refresh(key, fetchFunction) {
        this.remove(key);
        if (typeof fetchFunction === 'function') {
            return fetchFunction();
        }
    }

    /**
     * Get cache size in human readable format
     */
    getHumanReadableSize(bytes) {
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        if (bytes === 0) return '0 Bytes';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }

    /**
     * Initialize cache manager and clean expired caches
     */
    init() {
        // Clean expired caches on initialization
        const cleaned = this.cleanExpired();
        if (cleaned > 0) {
            console.log(`Cache Manager: Cleaned ${cleaned} expired cache entries`);
        }

        // Set up periodic cleanup (every 10 minutes)
        setInterval(() => {
            this.cleanExpired();
        }, 10 * 60 * 1000);

        console.log('Cache Manager initialized');
    }
}

// Create global instance
window.CacheManager = new CacheManager();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.CacheManager.init();
    });
} else {
    window.CacheManager.init();
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CacheManager;
}
