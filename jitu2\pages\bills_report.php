<?php
session_start();
include 'db_connect.php';

$conn->set_charset('latin1');

if (!isset($_SESSION['username'])) {
    die("<script>alert('You must be logged in!'); window.location.href='../login.php';</script>");
}

$username = $_SESSION['username'];

// Fetch all invoices grouped by customer
$customer_query = "SELECT inv_cust, COUNT(*) as invoice_count, SUM(inv_value) as total_value
                   FROM invoice
                   WHERE username = ?
                   GROUP BY inv_cust
                   ORDER BY inv_cust ASC";
$customer_stmt = $conn->prepare($customer_query);
$customer_stmt->bind_param("s", $username);
$customer_stmt->execute();
$customer_result = $customer_stmt->get_result();

$customers = [];
$total_invoices = 0;
$grand_total = 0;

while ($row = $customer_result->fetch_assoc()) {
    $customers[$row['inv_cust']] = [
        'count' => $row['invoice_count'],
        'total' => $row['total_value'],
        'invoices' => []
    ];
    $total_invoices += $row['invoice_count'];
    $grand_total += $row['total_value'];
}

// Fetch all invoices for this user
$invoice_query = "SELECT * FROM invoice WHERE username = ? ORDER BY inv_cust, inv_dt DESC, inv_no DESC";
$invoice_stmt = $conn->prepare($invoice_query);
$invoice_stmt->bind_param("s", $username);
$invoice_stmt->execute();
$invoice_result = $invoice_stmt->get_result();
while ($row = $invoice_result->fetch_assoc()) {
    $customers[$row['inv_cust']]['invoices'][] = $row;
}

// Calculate summary statistics
$total_customers = count($customers);
$total_pending = 0;
$total_paid = 0;

foreach ($customers as $info) {
    foreach ($info['invoices'] as $inv) {
        if ($inv['inv_paysts'] == 'Pending') {
            $total_pending += $inv['inv_value'];
        } else if ($inv['inv_paysts'] == 'Paid') {
            $total_paid += $inv['inv_paid_amt'];
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bill Details Report</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        /* Critical styles */
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            overflow: hidden;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #2196F3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        #mainContent {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease-in;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .report-container {
            opacity: 0;
            transition: opacity 0.3s ease-in;
        }

        /* Main styles */
        :root {
            --primary-blue: #2196F3;
            --light-blue: #E3F2FD;
            --hover-blue: #1976D2;
            --text-dark: #2c3e50;
            --border-color: #e0e0e0;
            --background: #F8FAFC;
            --success-green: #2ec4b6;
            --danger-red: #ef476f;
        }

        .report-container {
            padding: 2rem;
            margin: 0 auto;
            max-width: 1400px;
            position: relative;
        }

        .header-section {
            background: white;
            padding: 1.5rem 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1.5rem;
        }

        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            margin-bottom: 1.5rem;
        }

        .page-title {
            color: var(--primary-blue);
            font-size: 1.75rem;
            font-weight: 600;
            margin: 0;
        }

        .header-controls {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            width: 100%;
        }

        .search-box {
            position: relative;
            min-width: 450px;
            max-width: 600px;
        }

        .search-box i {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            color: var(--primary-blue);
        }

        .search-box i.fa-search {
            left: 1rem;
        }

        .search-box i.clear-search {
            right: 1rem;
            cursor: pointer;
            opacity: 0.7;
            transition: opacity 0.2s ease;
        }

        .search-box i.clear-search:hover {
            opacity: 1;
        }

        .search-box input {
            width: 100%;
            padding: 0.8rem 2.5rem;
            border: 2px solid var(--light-blue);
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .search-box input:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
        }

        .search-box input::placeholder {
            color: #94a3b8;
        }

        .stats-badges {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            flex: 1;
            justify-content: flex-end;
        }

        .stat-badge {
            background: var(--light-blue);
            color: var(--primary-blue);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 500;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .customer-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .customer-header {
            background: var(--primary-blue);
            color: white;
            padding: 1rem 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .customer-name {
            font-size: 1.2rem;
            font-weight: 500;
        }

        .customer-stats {
            display: flex;
            gap: 1rem;
        }

        .data-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }

        .data-table th {
            background: var(--light-blue);
            color: var(--primary-blue);
            padding: 1rem;
            text-align: left;
            font-weight: 500;
        }

        .data-table td {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

        .status-badge {
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            display: inline-block;
        }

        .status-badge.Pending {
            background: #fff5f7;
            color: var(--danger-red);
        }

        .status-badge.Paid {
            background: #f0faf9;
            color: var(--success-green);
        }

        .status-badge.Partial {
            background: #fffbe5;
            color: #f7b731;
        }

        .no-results {
            text-align: center;
            padding: 2rem;
            color: #64748b;
            font-size: 1.1rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            margin: 1rem 0;
        }

        @media (max-width: 1024px) {
            .header-controls {
                flex-direction: column;
                align-items: stretch;
            }

            .search-box {
                min-width: 100%;
                max-width: 100%;
            }

            .download-btn {
                align-self: flex-start;
                margin-top: 1rem;
            }
        }

        @media (max-width: 768px) {
            .header-section {
                padding: 1rem;
            }

            .header-top {
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
            }

            .download-btn {
                align-self: flex-start;
            }

            .stats-badges {
                flex-direction: column;
            }

            .stat-badge {
                width: 100%;
            }
        }

        .download-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.7rem 1.2rem;
            background: var(--success-green);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            height: fit-content;
        }

        .download-btn:hover {
            background: var(--hover-green);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(46, 196, 182, 0.2);
        }

        .download-btn i {
            font-size: 1.1rem;
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Main Content -->
    <div id="mainContent">
        <div class="report-container">
            <div class="header-section">
                <div class="header-top">
                    <h1 class="page-title">
                        <i class="fas fa-file-invoice"></i>
                        Bill Details Report
                    </h1>
                    <button id="downloadCsv" class="download-btn" title="Download Report">
                        <i class="fas fa-file-excel"></i>
                        <span>Download Report</span>
                    </button>
                </div>
                <div class="header-controls">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="searchInput" placeholder="Search by customer or invoice number...">
                        <i class="fas fa-times clear-search" style="display: none;"></i>
                    </div>
                    <div class="stats-badges">
                        <div class="stat-badge">
                            <i class="fas fa-users"></i>
                            Total Customers: <span id="customerCount"><?php echo $total_customers; ?></span>
                        </div>
                        <div class="stat-badge">
                            <i class="fas fa-file-invoice"></i>
                            Total Invoices: <span id="invoiceCount"><?php echo $total_invoices; ?></span>
                        </div>
                        <div class="stat-badge">
                            <i class="fas fa-rupee-sign"></i>
                            Total Amount: ₹<span id="totalAmount"><?php echo number_format($grand_total, 2); ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <?php if (empty($customers)) { ?>
                <div class="customer-card">
                    <div class="customer-header">
                        <span>No invoices found</span>
                    </div>
                </div>
            <?php } else {
                foreach ($customers as $cust => $info) { ?>
                    <div class="customer-card">
                        <div class="customer-header">
                            <div class="customer-name">
                                <i class="fas fa-user"></i>
                                <?php echo htmlspecialchars($cust); ?>
                            </div>
                            <div class="customer-stats">
                                <span class="stat-badge">
                                    <i class="fas fa-file-invoice"></i>
                                    <?php echo $info['count']; ?> invoice<?php echo $info['count'] > 1 ? 's' : ''; ?>
                                </span>
                                <span class="stat-badge">
                                    <i class="fas fa-rupee-sign"></i>
                                    ₹<?php echo number_format($info['total'], 2); ?>
                                </span>
                            </div>
                        </div>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Invoice No</th>
                                    <th>Date</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Paid Amount</th>
                                    <th>Paid Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($info['invoices'] as $inv) { ?>
                                    <tr>
                                        <td data-label="Invoice No"><?php echo htmlspecialchars($inv['inv_no']); ?></td>
                                        <td data-label="Date"><?php echo date('d-M-Y', strtotime($inv['inv_dt'])); ?></td>
                                        <td data-label="Amount">₹<?php echo number_format($inv['inv_value'], 2); ?></td>
                                        <td data-label="Status">
                                            <span class="status-badge <?php echo $inv['inv_paysts']; ?>">
                                                <?php echo $inv['inv_paysts']; ?>
                                            </span>
                                        </td>
                                        <td data-label="Paid Amount">₹<?php echo number_format($inv['inv_paid_amt'], 2); ?></td>
                                        <td data-label="Paid Date">
                                            <?php echo $inv['inv_paid_dt'] && $inv['inv_paid_dt'] != '0000-00-00' ? 
                                                date('d-M-Y', strtotime($inv['inv_paid_dt'])) : '-'; ?>
                                        </td>
                                    </tr>
                                <?php } ?>
                            </tbody>
                        </table>
                    </div>
                <?php }
            } ?>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
    $(document).ready(function() {
        const loadingOverlay = document.getElementById('loadingOverlay');
        const mainContent = document.getElementById('mainContent');
        const reportContainer = document.querySelector('.report-container');
        
        // Ensure the loading overlay is visible initially
        loadingOverlay.style.visibility = 'visible';
        loadingOverlay.style.opacity = '1';
        
        // Hide main content initially
        mainContent.style.visibility = 'hidden';
        mainContent.style.opacity = '0';
        
        // Function to show content
        function showContent() {
            // First hide the loading overlay
            loadingOverlay.style.opacity = '0';
            
            // After a brief delay, show the main content
            setTimeout(() => {
                // Hide loading overlay completely
                loadingOverlay.style.visibility = 'hidden';
                
                // Show main content
                mainContent.style.visibility = 'visible';
                mainContent.style.opacity = '1';
                document.body.style.overflow = 'auto';
                
                // Show report container with a slight delay for smooth transition
                setTimeout(() => {
                    reportContainer.style.opacity = '1';
                }, 50);
            }, 300);
        }
        
        // Wait for everything to load
        if (document.readyState === 'complete') {
            showContent();
        } else {
            window.addEventListener('load', showContent);
        }

        // Search functionality
        let searchTimeout;
        const searchInput = $('#searchInput');
        const clearButton = $('.clear-search');
        const customerCards = $('.customer-card');
        let visibleCustomers = 0;
        let visibleInvoices = 0;
        let totalAmount = 0;

        function updateStats() {
            $('#customerCount').text(visibleCustomers);
            $('#invoiceCount').text(visibleInvoices);
            $('#totalAmount').text(totalAmount.toLocaleString('en-IN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }));
        }

        function calculateInitialStats() {
            visibleCustomers = customerCards.length;
            visibleInvoices = 0;
            totalAmount = 0;

            customerCards.each(function() {
                const invoices = $(this).find('tbody tr');
                visibleInvoices += invoices.length;
                invoices.each(function() {
                    const amount = parseFloat($(this).find('td:nth-child(3)').text().replace(/[^0-9.-]+/g, '')) || 0;
                    totalAmount += amount;
                });
            });

            updateStats();
        }

        function filterResults() {
            const searchTerm = searchInput.val().toLowerCase().trim();
            visibleCustomers = 0;
            visibleInvoices = 0;
            totalAmount = 0;
            let hasResults = false;

            // Show/hide clear button based on search term
            clearButton.toggle(searchTerm.length > 0);

            customerCards.each(function() {
                const card = $(this);
                const customerName = card.find('.customer-name').text().toLowerCase();
                const invoices = card.find('tbody tr');
                let customerVisible = false;
                let customerInvoices = 0;
                let customerAmount = 0;

                invoices.each(function() {
                    const invoice = $(this);
                    const invoiceNo = invoice.find('td:first').text().toLowerCase();
                    const amount = parseFloat(invoice.find('td:nth-child(3)').text().replace(/[^0-9.-]+/g, '')) || 0;

                    if (customerName.includes(searchTerm) || invoiceNo.includes(searchTerm)) {
                        invoice.show();
                        customerVisible = true;
                        customerInvoices++;
                        customerAmount += amount;
                    } else {
                        invoice.hide();
                    }
                });

                if (customerVisible) {
                    card.show();
                    visibleCustomers++;
                    visibleInvoices += customerInvoices;
                    totalAmount += customerAmount;
                    hasResults = true;
                } else {
                    card.hide();
                }
            });

            // Show/hide no results message
            if (!hasResults) {
                if ($('.no-results').length === 0) {
                    $('.report-container').append('<div class="no-results">No matching records found</div>');
                }
            } else {
                $('.no-results').remove();
            }

            updateStats();
        }

        // Clear search
        clearButton.on('click', function() {
            searchInput.val('');
            clearButton.hide();
            filterResults();
        });

        searchInput.on('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(filterResults, 300);
        });

        // Initialize stats on page load
        calculateInitialStats();

        // Download CSV functionality
        $('#downloadCsv').on('click', function() {
            let csvContent = "data:text/csv;charset=utf-8,";
            
            // Add headers
            csvContent += "Customer,Invoice No,Date,Amount,Status,Paid Amount,Paid Date\n";
            
            // Add data rows
            $('.customer-card').each(function() {
                const customerName = $(this).find('.customer-name').text().trim();
                $(this).find('tbody tr').each(function() {
                    const row = [];
                    row.push('"' + customerName + '"'); // Customer name
                    $(this).find('td').each(function(index) {
                        let cellContent = $(this).text().trim();
                        // Remove currency symbol and format numbers
                        if (index === 2 || index === 4) { // Amount and Paid Amount columns
                            cellContent = cellContent.replace(/[₹,]/g, '');
                        }
                        row.push('"' + cellContent + '"');
                    });
                    csvContent += row.join(",") + "\n";
                });
            });
            
            // Create download link
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "bill_details_" + new Date().toISOString().slice(0,10) + ".csv");
            document.body.appendChild(link);
            
            // Trigger download
            link.click();
            document.body.removeChild(link);
        });
    });
    </script>
</body>
</html>