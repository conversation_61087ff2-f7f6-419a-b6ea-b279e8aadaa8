<?php
// Always start session if not already started
if (!isset($_SESSION)) {
    session_start();
}

// Set content type to JSON first
header('Content-Type: application/json');

// Include database connection
include '../db_connect.php';

if (!isset($_SESSION['username'])) {
    echo json_encode([
        'success' => false,
        'message' => 'You must be logged in.'
    ]);
    exit();
}

$username = $_SESSION['username'];

try {
    $search_query = $_GET['search'] ?? '';
    $data = [];
    
    if (!empty($search_query)) {
        // Search across short_name, p_p (company name), and gst_no
        $sql = "SELECT id, short_name, p_p, gst_no 
                FROM customers 
                WHERE username = ? 
                AND (short_name LIKE ? OR p_p LIKE ? OR gst_no LIKE ?)
                ORDER BY short_name ASC";
                    
        $stmt = $conn->prepare($sql);
        $search_pattern = "%{$search_query}%";
        $stmt->bind_param("ssss", $username, $search_pattern, $search_pattern, $search_pattern);
    } else {
        // Return all customers if no search query
        $sql = "SELECT id, short_name, p_p, gst_no 
                FROM customers 
                WHERE username = ? 
                ORDER BY short_name ASC";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $username);
    }
    
    if (!$stmt->execute()) {
        throw new Exception("Database error: " . $stmt->error);
    }
    
    $result = $stmt->get_result();
    
    while ($row = $result->fetch_assoc()) {
        $data[] = [
            'id' => $row['id'],
            'short_name' => $row['short_name'],
            'p_p' => $row['p_p'],
            'gst_no' => $row['gst_no']
        ];
    }
    
    echo json_encode([
        'success' => true,
        'data' => $data,
        'total' => count($data),
        'search_query' => $search_query
    ]);

    $stmt->close();

    // Close connection and exit to prevent any extra output
    if (isset($conn)) {
        $conn->close();
    }
    exit();
    
} catch (Exception $e) {
    error_log("Customer search error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Search failed. Please try again.',
        'error' => $e->getMessage()
    ]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}

// Ensure no extra output
exit();
?>
