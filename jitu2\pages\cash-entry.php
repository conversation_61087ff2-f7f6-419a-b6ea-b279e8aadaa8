<?php
// Cash Entry Page
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cash Entry</title>
    <!-- Add jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Add Cache Manager -->
    <script src="js/cache-manager.js"></script>
    <style>
        :root {
            --primary-color: #2196F3;
            --secondary-color: #03A9F4;
            --accent-color: #00BCD4;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --background-color: #ffffff;
            --text-color: #1f2937;
            --text-muted: #64748b;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --header-height: 70px;
            --glass-bg: rgba(255, 255, 255, 0.98);
            --glass-border: rgba(255, 255, 255, 0.2);
            --text-dark: #1F2937;
            --text-light: #6B7280;
            --background: #F3F4F6;
            --card-background: #FFFFFF;
            --border-color: #E5E7EB;
            --shadow-light: 0 1px 3px rgba(0,0,0,0.1);
            --shadow-medium: 0 4px 6px rgba(0,0,0,0.1);
            --shadow-heavy: 0 10px 25px rgba(0,0,0,0.15);
            --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            --gradient-secondary: linear-gradient(135deg, var(--accent-color), var(--primary-color));
            --gradient-accent: linear-gradient(135deg, var(--success-color), #059669);
        }

        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            background: var(--background);
            background-image:
                radial-gradient(circle at 20% 80%, rgba(33, 150, 243, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(3, 169, 244, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(0, 188, 212, 0.05) 0%, transparent 50%);
            font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--text-dark);
            line-height: 1.6;
        }

        .content-wrapper {
            display: flex;
            gap: 2rem;
            max-width: 1100px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .content-box {
            flex: 1;
            max-width: 800px;
            padding: 2rem 2.5rem;
            background: var(--card-background);
            border-radius: 20px;
            box-shadow: var(--shadow-heavy);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            font-size: 16px;
            margin: 0;
            position: relative;
            overflow: hidden;
        }

        .content-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        .form-container {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            max-width: 600px;
            margin: 0 auto;
        }

        .form-group {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 0;
        }

        .form-group label {
            width: 120px;
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-dark);
            margin-bottom: 0;
            flex-shrink: 0;
        }

        .form-group input,
        .form-group select {
            flex: 1;
            padding: 0.75rem 1rem;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            font-size: 0.95rem;
            background: var(--card-background);
            color: var(--text-dark);
            transition: all 0.3s ease;
            box-shadow: var(--shadow-light);
            min-height: 44px;
            box-sizing: border-box;
            max-width: calc(100% - 140px);
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
            transform: translateY(-1px);
        }

        .form-group input:hover,
        .form-group select:hover {
            border-color: var(--secondary-color);
            box-shadow: var(--shadow-medium);
        }

        /* Header Section Styles */
        .form-header {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1.5rem;
            margin: 0 auto 2rem auto;
            padding: 1rem 2rem;
            background: var(--gradient-primary);
            border-radius: 16px;
            width: 100%;
            box-shadow: var(--shadow-medium);
            position: relative;
            overflow: hidden;
            white-space: nowrap;
            flex-wrap: nowrap;
        }

        .form-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
            pointer-events: none;
        }

        .form-title {
            color: white;
            font-size: 1.8rem;
            font-weight: 700;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
            letter-spacing: -0.5px;
        }

        /* Toggle Switch Styles */
        .toggle-container {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            flex-shrink: 0;
        }

        .toggle-label {
            font-size: 0.9rem;
            font-weight: 600;
            color: white;
            margin: 0;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        .toggle-switch {
            position: relative;
            width: 56px;
            height: 28px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .toggle-switch.active {
            background: rgba(255, 255, 255, 0.3);
        }

        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 22px;
            height: 22px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .toggle-switch.active .toggle-slider {
            transform: translateX(28px);
        }

        .form-group input[readonly] {
            background-color: #f5f5f5;
            cursor: not-allowed;
        }

        /* Button Styles */
        .button-container {
            margin-top: 2rem;
            text-align: center;
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        button {
            padding: 0.875rem 2rem;
            border: none;
            border-radius: 12px;
            font-size: 0.95rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: var(--shadow-medium);
            position: relative;
            overflow: hidden;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-heavy);
        }

        button:active {
            transform: translateY(0);
        }

        button[type="submit"] {
            background: var(--gradient-primary);
            color: white;
            min-width: 140px;
        }

        button[type="submit"]:hover {
            background: linear-gradient(135deg, #1976D2, #0288D1);
        }

        button[type="reset"] {
            background: var(--gradient-secondary);
            color: white;
            min-width: 120px;
        }

        button[type="reset"]:hover {
            background: linear-gradient(135deg, #00ACC1, #1976D2);
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .content-wrapper {
                flex-direction: column;
            }

            .upload-section {
                width: auto;
                max-width: 800px;
                margin: 0 auto;
            }
        }

        @media (max-width: 768px) {
            .content-wrapper {
                margin: 1rem;
                gap: 1rem;
            }

            .content-box {
                padding: 1rem;
            }

            .form-header {
                width: 95%;
                padding: 0.75rem 1.5rem;
                gap: 1rem;
                justify-content: center;
                flex-wrap: nowrap;
                white-space: nowrap;
            }

            .form-title {
                font-size: 1.4rem;
            }

            .form-group {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .form-group label {
                width: auto;
                font-size: 0.85rem;
            }

            .button-container {
                flex-direction: column;
                align-items: center;
            }

            button {
                width: 100%;
                max-width: 200px;
            }
        }

        /* Loading overlay styles */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(5px);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(33, 150, 243, 0.1);
            border-top: 5px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        #mainContent {
            opacity: 1;
            visibility: visible;
            transition: opacity 0.3s ease-in;
        }

            .form-group label {
                width: 100%;
            }

            .form-group input,
            .form-group select {
                width: 100%;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Main content container -->
    <div id="mainContent">
<?php
// Include database connection for session validation only
include 'db_connect.php';

// Initialize empty variables - data will be loaded via AJAX
$cn_numbers = [];
$regionSeries = '';
$docketInitials = [
    'surface' => '',
    'aircargo' => '',
    'premium' => '',
    'ptp' => '',
    'cod' => '',
    'international' => '',
    'ecom_express' => '',
    'ecom_surface' => ''
];
?>

<div class="content-wrapper">
    <div class="content-box">
        <!-- Form Header with Title and Toggle -->
        <div class="form-header">
            <h2 class="form-title">Cash Entry</h2>
            <div class="toggle-container">
                <label class="toggle-label">Entry without Quote</label>
                <div class="toggle-switch" id="entryWithoutQuoteToggle" onclick="toggleEntryMode()">
                    <div class="toggle-slider"></div>
                </div>
            </div>
        </div>

        <form action="process/process_cash_entry.php" method="POST" autocomplete="off" onsubmit="return validateForm()">
            <div class="form-container">
                <div class="form-group">
                    <label for="docket_no">Docket No.:</label>
                    <input type="text" id="docket_no" name="docket_no" list="cn_list" required onkeyup="this.value = this.value.toUpperCase(); updateModeOfTSP()">
                    <datalist id="cn_list">
                        <option value="Loading..."></option>
                    </datalist>
                </div>

                <div class="form-group">
                    <label for="docket_date">Docket Date:</label>
                    <input type="date" id="docket_date" name="docket_date" required value="<?php echo date('Y-m-d'); ?>">
                </div>

                <div class="form-group">
                    <label for="pincode">Pincode:</label>
                    <input type="text" id="pincode" name="pincode" required onkeyup="fetchCity()" maxlength="6" onkeypress="return event.charCode >= 48 && event.charCode <= 57">
                </div>

                <div class="form-group">
                    <label for="destination">Destination:</label>
                    <input type="text" id="destination" name="destination" readonly>
                </div>

                <div class="form-group">
                    <label for="weight">Weight:</label>
                    <input type="text" id="weight" name="weight" required>
                </div>

                <div class="form-group">
                    <label for="amount">Amount:</label>
                    <input type="text" id="amount" name="amount">
                </div>

                <div class="form-group">
                    <label for="payment_status">Payment Status:</label>
                    <select id="payment_status" name="payment_status" required>
                        <option value="" selected>-- Select --</option>
                        <option value="Cash-Received">Cash-Received</option>
                        <option value="Online-Received">Online-Received</option>
                        <option value="Pending">Pending</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="mode_of_tsp">Mode of TSP:</label>
                    <select id="mode_of_tsp" name="mode_of_tsp" required>
                        <option value="">-- Select --</option>
                        <option value="Express">Express</option>
                        <option value="Surface">Surface</option>
                        <option value="Air Cargo">Air Cargo</option>
                        <option value="Premium">Premium</option>
                        <option value="PTP">PTP</option>
                        <option value="COD">COD</option>
                        <option value="International">International</option>
                        <option value="E-Com Express">E-Com Express</option>
                        <option value="E-Com Surface">E-Com Surface</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="remarks">Remarks:</label>
                    <input type="text" id="remarks" name="remarks">
                </div>

                <div class="form-group">
                    <label for="mobile1">Mobile #1:</label>
                    <input type="text" id="mobile1" name="mobile1">
                </div>

                <div class="form-group">
                    <label for="mobile2">Mobile #2:</label>
                    <input type="text" id="mobile2" name="mobile2">
                </div>

                <div class="button-container">
                    <button type="submit">Submit</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
function fetchCity() {
    let pincodeInput = document.getElementById("pincode");
    let destinationInput = document.getElementById("destination");
    let weightInput = document.getElementById("weight"); 
    let pincode = pincodeInput.value.trim();

    if (pincode.length !== 6 || isNaN(pincode)) {
        destinationInput.value = "";
        return;
    }

    let xhr = new XMLHttpRequest();
    xhr.open("GET", "get_city.php?pincode=" + pincode, true);
    xhr.onreadystatechange = function () {
        if (xhr.readyState === 4 && xhr.status === 200) {
            let city = xhr.responseText.trim();
            destinationInput.value = (city !== "Not Found") ? city : "";

            if (city !== "Not Found") {
                weightInput.focus();
            }
        }
    };
    xhr.send();
}
</script>

<script>
function updateModeOfTSP() {
    let docketNo = document.getElementById("docket_no").value.toUpperCase();
    let modeOfTSP = document.getElementById("mode_of_tsp");

    modeOfTSP.innerHTML = "";

    // Use globally loaded data
    if (docketNo.startsWith(regionSeries)) {
        modeOfTSP.innerHTML = '<option value="Express">Express</option>';
    } else if (docketNo.startsWith(docketInitials.surface)) {
        modeOfTSP.innerHTML = '<option value="Surface">Surface</option><option value="Air Cargo">Air Cargo</option>';
    } else if (docketNo.startsWith(docketInitials.premium)) {
        modeOfTSP.innerHTML = '<option value="Premium">Premium</option>';
    } else if (docketNo.startsWith(docketInitials.ptp)) {
        modeOfTSP.innerHTML = '<option value="PTP">PTP</option>';
    } else if (docketNo.startsWith(docketInitials.cod)) {
        modeOfTSP.innerHTML = '<option value="COD">COD</option>';
    } else if (docketNo.startsWith(docketInitials.international)) {
        modeOfTSP.innerHTML = '<option value="International">International</option>';
    } else if (docketNo.startsWith(docketInitials.ecom_express)) {
        modeOfTSP.innerHTML = '<option value="E-Com Express">E-Com Express</option>';
    } else if (docketNo.startsWith(docketInitials.ecom_surface)) {
        modeOfTSP.innerHTML = '<option value="E-Com Surface">E-Com Surface</option>';
    } else {
        modeOfTSP.innerHTML = `
            <option value="">-- Select --</option>
            <option value="Express">Express</option>
            <option value="Surface">Surface</option>
            <option value="Air Cargo">Air Cargo</option>
            <option value="Premium">Premium</option>
            <option value="PTP">PTP</option>
            <option value="COD">COD</option>
            <option value="International">International</option>
            <option value="E-Com Express">E-Com Express</option>
            <option value="E-Com Surface">E-Com Surface</option>
        `;
    }
}
</script>

<script>
// Global variables for lazy loaded data
let regionSeries = '';
let docketInitials = {};

// Cache configuration using centralized cache manager
const CACHE_KEY = window.CacheManager.cacheKeys.CASH_ENTRY;

// Populate page with data
function populatePageData(data) {
    // Populate CN numbers datalist
    const cnList = document.getElementById('cn_list');
    cnList.innerHTML = '';

    if (data.cn_numbers && data.cn_numbers.length > 0) {
        data.cn_numbers.forEach(cn => {
            const option = document.createElement('option');
            option.value = cn;
            cnList.appendChild(option);
        });
    } else {
        const option = document.createElement('option');
        option.value = 'No Available CN Numbers';
        cnList.appendChild(option);
    }

    // Store settings data globally
    regionSeries = data.settings.region_series || '';
    docketInitials = data.settings.docket_initials || {};
}

// Lazy load data function with caching
function loadPageData() {
    // Try to load from cache first
    const cachedData = window.CacheManager.get(CACHE_KEY);
    if (cachedData) {
        populatePageData(cachedData);
        return;
    }

    // If no cache, fetch from server
    fetch('process/get_page_data.php?type=cash')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Populate page
                populatePageData(data);

                // Save to cache for future use
                window.CacheManager.set(CACHE_KEY, data);
            }
        })
        .catch(error => {
            console.error('Error loading page data:', error);
            // Fallback - show error in datalist
            const cnList = document.getElementById('cn_list');
            cnList.innerHTML = '<option value="Error loading data">Error loading data</option>';
        });
}

// Function to refresh cache manually
function refreshCache() {
    window.CacheManager.refresh(CACHE_KEY, loadPageData);
}

$(document).ready(function() {
    // Load data via AJAX after page is ready
    loadPageData();

    // Focus on the first input field with a small delay
    setTimeout(() => {
        const docketInput = document.getElementById('docket_no');
        if (docketInput) {
            docketInput.focus();
            docketInput.select();
        }
    }, 100);
});

// Toggle functionality for Entry without Quote
function toggleEntryMode() {
    const toggle = document.getElementById('entryWithoutQuoteToggle');
    const isActive = toggle.classList.contains('active');

    if (isActive) {
        toggle.classList.remove('active');
        // Enable quote-based entry mode
        console.log('Quote-based entry mode enabled');
    } else {
        toggle.classList.add('active');
        // Enable manual entry mode
        console.log('Manual entry mode enabled');
    }
}

// Form validation
function validateForm() {
    const docketNo = document.getElementById('docket_no').value.trim();
    const customer = document.getElementById('customer').value.trim();

    if (!docketNo) {
        alert('Please enter Docket Number');
        document.getElementById('docket_no').focus();
        return false;
    }

    if (!customer) {
        alert('Please select a Customer');
        document.getElementById('customer').focus();
        return false;
    }

    return true;
}
</script>

</div>
</body>
</html>
