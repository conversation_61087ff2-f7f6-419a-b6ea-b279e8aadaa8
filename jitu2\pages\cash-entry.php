<?php
// Cash Entry Page
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cash Entry</title>
    <!-- Add jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Add Cache Manager -->
    <script src="js/cache-manager.js"></script>
    <style>
        :root {
            --primary-blue: #2196F3;
            --light-blue: #E3F2FD;
            --hover-blue: #1976D2;
            --sky-blue: #87CEEB;
            --text-dark: #2c3e50;
            --border-color: #e0e0e0;
            --background: #F8FAFC;
        }

        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            background: var(--background);
            font-family: system-ui, -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, Oxygen, Ubuntu, Can<PERSON><PERSON>, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        .content-wrapper {
            display: flex;
            gap: 2rem;
            max-width: 1400px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .content-box {
            flex: 1;
            max-width: 800px;
            padding: 1.5rem 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            font-size: 16px;
            margin: 0;
        }

        h2 {
            color: var(--primary-blue);
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0 0 1.5rem 0;
            text-align: center;
        }

        .form-container {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            max-width: 600px;
            margin: 0 auto;
        }

        .form-group {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 0;
        }

        .form-group label {
            width: 120px;
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-dark);
            margin-bottom: 0;
            flex-shrink: 0;
        }

        .form-group input,
        .form-group select {
            flex: 1;
            height: 32px;
            padding: 0.25rem 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 0.9rem;
            color: var(--text-dark);
            background: var(--background);
            transition: all 0.3s ease;
            max-width: 300px;
        }

        .form-group input[readonly] {
            background-color: #f5f5f5;
            cursor: not-allowed;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary-blue);
            background: white;
            box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
        }

        .button-container {
            margin-top: 1rem;
            text-align: center;
        }

        button {
            width: auto;
            min-width: 120px;
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
            font-weight: 500;
            background-color: var(--primary-blue);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        button:hover {
            background-color: var(--hover-blue);
            transform: translateY(-1px);
        }

        button:active {
            transform: translateY(0);
        }

        /* Loading overlay styles */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid var(--primary-blue);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        #mainContent {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease-in;
        }

        @media (max-width: 768px) {
            .content-wrapper {
                margin: 1rem;
                gap: 1rem;
            }

            .content-box {
                padding: 1rem;
            }

            .form-group {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.25rem;
            }

            .form-group label {
                width: 100%;
            }

            .form-group input,
            .form-group select {
                width: 100%;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Main content container -->
    <div id="mainContent">
<?php
// Include database connection for session validation only
include 'db_connect.php';

// Initialize empty variables - data will be loaded via AJAX
$cn_numbers = [];
$regionSeries = '';
$docketInitials = [
    'surface' => '',
    'aircargo' => '',
    'premium' => '',
    'ptp' => '',
    'cod' => '',
    'international' => '',
    'ecom_express' => '',
    'ecom_surface' => ''
];
?>

<div class="content-wrapper">
    <div class="content-box">
        <h2>Cash Entry</h2>
        <form action="process/process_cash_entry.php" method="POST" autocomplete="off">
            <div class="form-container">
                <div class="form-group">
                    <label for="docket_no">Docket No.:</label>
                    <input type="text" id="docket_no" name="docket_no" list="cn_list" required onkeyup="this.value = this.value.toUpperCase(); updateModeOfTSP()">
                    <datalist id="cn_list">
                        <option value="Loading..."></option>
                    </datalist>
                </div>

                <div class="form-group">
                    <label for="docket_date">Docket Date:</label>
                    <input type="date" id="docket_date" name="docket_date" required value="<?php echo date('Y-m-d'); ?>">
                </div>

                <div class="form-group">
                    <label for="pincode">Pincode:</label>
                    <input type="text" id="pincode" name="pincode" required onkeyup="fetchCity()" maxlength="6" onkeypress="return event.charCode >= 48 && event.charCode <= 57">
                </div>

                <div class="form-group">
                    <label for="destination">Destination:</label>
                    <input type="text" id="destination" name="destination" readonly>
                </div>

                <div class="form-group">
                    <label for="weight">Weight:</label>
                    <input type="text" id="weight" name="weight" required>
                </div>

                <div class="form-group">
                    <label for="amount">Amount:</label>
                    <input type="text" id="amount" name="amount">
                </div>

                <div class="form-group">
                    <label for="payment_status">Payment Status:</label>
                    <select id="payment_status" name="payment_status" required>
                        <option value="" selected>-- Select --</option>
                        <option value="Cash-Received">Cash-Received</option>
                        <option value="Online-Received">Online-Received</option>
                        <option value="Pending">Pending</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="mode_of_tsp">Mode of TSP:</label>
                    <select id="mode_of_tsp" name="mode_of_tsp" required>
                        <option value="">-- Select --</option>
                        <option value="Express">Express</option>
                        <option value="Surface">Surface</option>
                        <option value="Air Cargo">Air Cargo</option>
                        <option value="Premium">Premium</option>
                        <option value="PTP">PTP</option>
                        <option value="COD">COD</option>
                        <option value="International">International</option>
                        <option value="E-Com Express">E-Com Express</option>
                        <option value="E-Com Surface">E-Com Surface</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="remarks">Remarks:</label>
                    <input type="text" id="remarks" name="remarks">
                </div>

                <div class="form-group">
                    <label for="mobile1">Mobile #1:</label>
                    <input type="text" id="mobile1" name="mobile1">
                </div>

                <div class="form-group">
                    <label for="mobile2">Mobile #2:</label>
                    <input type="text" id="mobile2" name="mobile2">
                </div>

                <div class="button-container">
                    <button type="submit">Submit</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
function fetchCity() {
    let pincodeInput = document.getElementById("pincode");
    let destinationInput = document.getElementById("destination");
    let weightInput = document.getElementById("weight"); 
    let pincode = pincodeInput.value.trim();

    if (pincode.length !== 6 || isNaN(pincode)) {
        destinationInput.value = "";
        return;
    }

    let xhr = new XMLHttpRequest();
    xhr.open("GET", "get_city.php?pincode=" + pincode, true);
    xhr.onreadystatechange = function () {
        if (xhr.readyState === 4 && xhr.status === 200) {
            let city = xhr.responseText.trim();
            destinationInput.value = (city !== "Not Found") ? city : "";

            if (city !== "Not Found") {
                weightInput.focus();
            }
        }
    };
    xhr.send();
}
</script>

<script>
function updateModeOfTSP() {
    let docketNo = document.getElementById("docket_no").value.toUpperCase();
    let modeOfTSP = document.getElementById("mode_of_tsp");

    modeOfTSP.innerHTML = "";

    // Use globally loaded data
    if (docketNo.startsWith(regionSeries)) {
        modeOfTSP.innerHTML = '<option value="Express">Express</option>';
    } else if (docketNo.startsWith(docketInitials.surface)) {
        modeOfTSP.innerHTML = '<option value="Surface">Surface</option><option value="Air Cargo">Air Cargo</option>';
    } else if (docketNo.startsWith(docketInitials.premium)) {
        modeOfTSP.innerHTML = '<option value="Premium">Premium</option>';
    } else if (docketNo.startsWith(docketInitials.ptp)) {
        modeOfTSP.innerHTML = '<option value="PTP">PTP</option>';
    } else if (docketNo.startsWith(docketInitials.cod)) {
        modeOfTSP.innerHTML = '<option value="COD">COD</option>';
    } else if (docketNo.startsWith(docketInitials.international)) {
        modeOfTSP.innerHTML = '<option value="International">International</option>';
    } else if (docketNo.startsWith(docketInitials.ecom_express)) {
        modeOfTSP.innerHTML = '<option value="E-Com Express">E-Com Express</option>';
    } else if (docketNo.startsWith(docketInitials.ecom_surface)) {
        modeOfTSP.innerHTML = '<option value="E-Com Surface">E-Com Surface</option>';
    } else {
        modeOfTSP.innerHTML = `
            <option value="">-- Select --</option>
            <option value="Express">Express</option>
            <option value="Surface">Surface</option>
            <option value="Air Cargo">Air Cargo</option>
            <option value="Premium">Premium</option>
            <option value="PTP">PTP</option>
            <option value="COD">COD</option>
            <option value="International">International</option>
            <option value="E-Com Express">E-Com Express</option>
            <option value="E-Com Surface">E-Com Surface</option>
        `;
    }
}
</script>

<script>
// Global variables for lazy loaded data
let regionSeries = '';
let docketInitials = {};

// Cache configuration using centralized cache manager
const CACHE_KEY = window.CacheManager.cacheKeys.CASH_ENTRY;

// Populate page with data
function populatePageData(data) {
    // Populate CN numbers datalist
    const cnList = document.getElementById('cn_list');
    cnList.innerHTML = '';

    if (data.cn_numbers && data.cn_numbers.length > 0) {
        data.cn_numbers.forEach(cn => {
            const option = document.createElement('option');
            option.value = cn;
            cnList.appendChild(option);
        });
    } else {
        const option = document.createElement('option');
        option.value = 'No Available CN Numbers';
        cnList.appendChild(option);
    }

    // Store settings data globally
    regionSeries = data.settings.region_series || '';
    docketInitials = data.settings.docket_initials || {};
}

// Lazy load data function with caching
function loadPageData() {
    // Try to load from cache first
    const cachedData = window.CacheManager.get(CACHE_KEY);
    if (cachedData) {
        populatePageData(cachedData);
        return;
    }

    // If no cache, fetch from server
    fetch('process/get_page_data.php?type=cash')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Populate page
                populatePageData(data);

                // Save to cache for future use
                window.CacheManager.set(CACHE_KEY, data);
            }
        })
        .catch(error => {
            console.error('Error loading page data:', error);
            // Fallback - show error in datalist
            const cnList = document.getElementById('cn_list');
            cnList.innerHTML = '<option value="Error loading data">Error loading data</option>';
        });
}

// Function to refresh cache manually
function refreshCache() {
    window.CacheManager.refresh(CACHE_KEY, loadPageData);
}

$(document).ready(function() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    const mainContent = document.getElementById('mainContent');

    // Ensure the loading overlay is visible initially
    loadingOverlay.style.visibility = 'visible';
    loadingOverlay.style.opacity = '1';

    // Hide main content initially
    mainContent.style.visibility = 'hidden';
    mainContent.style.opacity = '0';

    // Function to show content
    function showContent() {
        // First hide the loading overlay
        loadingOverlay.style.opacity = '0';

        // After a brief delay, show the main content
        setTimeout(() => {
            // Hide loading overlay completely
            loadingOverlay.style.visibility = 'hidden';

            // Show main content
            mainContent.style.visibility = 'visible';
            mainContent.style.opacity = '1';
            document.body.style.overflow = 'auto';

            // Load data via AJAX after page is visible
            loadPageData();

            // Select the text in the first input field with a small delay
            setTimeout(() => {
                const docketInput = document.getElementById('docket_no');
                if (docketInput) {
                    docketInput.focus();
                    docketInput.select();
                }
            }, 100);
        }, 300);
    }

    // Wait for everything to load
    if (document.readyState === 'complete') {
        showContent();
    } else {
        window.addEventListener('load', showContent);
    }
});
</script>

</div>
</body>
</html>
