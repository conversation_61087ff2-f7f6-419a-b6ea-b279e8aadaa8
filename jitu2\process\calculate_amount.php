<?php
  error_reporting(E_ALL);
  ini_set('display_errors', 1);
  ?>
<?php
include '../db_connect.php';

header('Content-Type: application/json');

if (!isset($_GET['weight']) || !isset($_GET['customer']) || !isset($_GET['mode_of_tsp']) || !isset($_GET['pincode'])) {
    echo json_encode(['success' => false, 'error' => 'Missing required parameters']);
    exit;
}

$weight = floatval($_GET['weight']);
$customer = $_GET['customer'];
$mode_of_tsp = $_GET['mode_of_tsp'];
$pincode = $_GET['pincode'];

// Fetch Zone from Pincode
$zone = '';
$zone_query = "SELECT zone FROM pincode_data WHERE pincode=?";
$zone_stmt = $conn->prepare($zone_query);
$zone_stmt->bind_param("s", $pincode);
$zone_stmt->execute();
$zone_result = $zone_stmt->get_result();
if ($zone_row = $zone_result->fetch_assoc()) {
    $zone = $zone_row['zone'];
}
$zone_stmt->close();

// Fetch Rates from Rate Master
$rate_query = "SELECT * FROM rate_master WHERE short_name=? AND mode_of_tsp=? AND zone=?";
$rate_stmt = $conn->prepare($rate_query);
$rate_stmt->bind_param("sss", $customer, $mode_of_tsp, $zone);
$rate_stmt->execute();
$rate_result = $rate_stmt->get_result();
$amount = 0;

if ($rate_row = $rate_result->fetch_assoc()) {
    // Round weight to nearest 0.5 kg if above 250 grams
    if ($weight > 0.250) {
        $weight = ceil($weight * 2) / 2;
    }

    if ($weight <= 0.250) {
        // First 250 grams
        $amount = floatval($rate_row['up_to_0250']);
    } elseif ($weight <= 0.500) {
        // Up to 500 grams
        $amount = floatval($rate_row['up_to_0500']);
    } elseif ($weight <= 10.000) {
        // From 500g to 10kg - calculate additional 500 grams
        $additional_500g_units = ceil(($weight - 0.500) / 0.500);
        $amount = floatval($rate_row['up_to_0500']) + ($additional_500g_units * floatval($rate_row['addl_500gm']));
    } else {
        // Above 10kg - apply flat per kg rate
        $amount = ceil($weight) * floatval($rate_row['above_3kg']);
    }
}
$rate_stmt->close();

echo json_encode([
    'success' => true,
    'amount' => round($amount, 2) // Round to 2 decimal places
]);

$conn->close();
?> 