<?php
session_start();
include 'db_connect.php';

if (!isset($_SESSION['username'])) {
    die("<script>alert('You must be logged in!'); window.location.href='../login.php';</script>");
}

$username = $_SESSION['username'];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Initialize variables
    $id = isset($_POST['id']) ? $_POST['id'] : null;
    $mode_of_tsp = $_POST['mode_of_tsp'];
    $zone = $_POST['zone'];
    
    // Initialize all rate fields with NULL
    $rates = [
        'up_to_0100' => null,
        'up_to_0250' => null,
        'up_to_0500' => null,
        'addl_500gm' => null,
        'up_to_5kg' => null,
        'up_to_10kg' => null,
        'up_to_25kg' => null,
        'up_to_50kg' => null,
        'above_50kg' => null,
        'min_weight' => null
    ];

    // Set values based on mode
    foreach ($rates as $field => $value) {
        if (isset($_POST[$field]) && $_POST[$field] !== '') {
            $rates[$field] = floatval($_POST[$field]);
        }
    }

    try {
        if ($id) {
            // Update existing record
            $sql = "UPDATE ts_rate_master SET 
                    mode_of_tsp = ?, 
                    zone = ?, 
                    up_to_0100 = ?, 
                    up_to_0250 = ?, 
                    up_to_0500 = ?, 
                    addl_500gm = ?, 
                    up_to_5kg = ?, 
                    up_to_10kg = ?, 
                    up_to_25kg = ?, 
                    up_to_50kg = ?, 
                    above_50kg = ?,
                    min_weight = ? 
                    WHERE id = ? AND username = ?";
            
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("ssddddddddddis", 
                $mode_of_tsp, 
                $zone, 
                $rates['up_to_0100'], 
                $rates['up_to_0250'], 
                $rates['up_to_0500'], 
                $rates['addl_500gm'], 
                $rates['up_to_5kg'], 
                $rates['up_to_10kg'], 
                $rates['up_to_25kg'], 
                $rates['up_to_50kg'], 
                $rates['above_50kg'],
                $rates['min_weight'], 
                $id,
                $username
            );
        } else {
            // Insert new record
            $sql = "INSERT INTO ts_rate_master (
                    username,
                    mode_of_tsp, 
                    zone, 
                    up_to_0100, 
                    up_to_0250, 
                    up_to_0500, 
                    addl_500gm, 
                    up_to_5kg, 
                    up_to_10kg, 
                    up_to_25kg, 
                    up_to_50kg, 
                    above_50kg,
                    min_weight,
                    created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("sssdddddddddd", 
                $username,
                $mode_of_tsp, 
                $zone, 
                $rates['up_to_0100'], 
                $rates['up_to_0250'], 
                $rates['up_to_0500'], 
                $rates['addl_500gm'], 
                $rates['up_to_5kg'], 
                $rates['up_to_10kg'], 
                $rates['up_to_25kg'], 
                $rates['up_to_50kg'], 
                $rates['above_50kg'],
                $rates['min_weight']
            );
        }

        if ($stmt->execute()) {
            echo "<script>
                alert('Rate " . ($id ? "updated" : "added") . " successfully!');
                window.location.href='index.php?page=ts_rate_master';
            </script>";
        } else {
            throw new Exception($stmt->error);
        }

    } catch (Exception $e) {
        echo "<script>
            alert('Error: " . addslashes($e->getMessage()) . "');
            window.location.href='index.php?page=ts_rate_master';
        </script>";
    }
} else {
    header("Location: index.php?page=ts_rate_master");
    exit();
}
?> 