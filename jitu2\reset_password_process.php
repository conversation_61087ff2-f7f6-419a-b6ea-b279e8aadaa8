<?php
require_once 'config.php';
require_once 'vendor/autoload.php'; // Ensure PHPMailer is included

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

header('Content-Type: application/json');

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Get POST data
$input = file_get_contents('php://input');
error_log("Received input: " . $input);

$data = json_decode($input, true);
if (json_last_error() !== JSON_ERROR_NONE) {
    error_log("JSON decode error: " . json_last_error_msg());
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request data.'
    ]);
    exit;
}

$token = $data['token'] ?? '';
$password = $data['password'] ?? '';

if (empty($token) || empty($password)) {
    error_log("Missing required fields - Token: " . (empty($token) ? 'empty' : 'present') . 
              ", Password: " . (empty($password) ? 'empty' : 'present'));
    echo json_encode([
        'success' => false,
        'message' => 'Missing required fields.'
    ]);
    exit;
}

try {
    // Verify token and get email
    $stmt = $pdo->prepare("SELECT email FROM password_resets WHERE token = ? AND expires_at > NOW() AND used = 0");
    if (!$stmt) {
        throw new Exception("Failed to prepare token verification query: " . implode(" ", $pdo->errorInfo()));
    }
    
    $stmt->execute([$token]);
    $reset = $stmt->fetch();

    if (!$reset) {
        error_log("Invalid or expired token: " . $token);
        echo json_encode([
            'success' => false,
            'message' => 'Invalid or expired password reset link. Please request a new one.'
        ]);
        exit;
    }

    $email = $reset['email'];
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    if ($hashedPassword === false) {
        throw new Exception("Failed to hash password");
    }

    // Update user's password using the correct column name password_hash
    $stmt = $pdo->prepare("UPDATE users SET password_hash = ? WHERE email = ?");
    if (!$stmt) {
        throw new Exception("Failed to prepare password update query: " . implode(" ", $pdo->errorInfo()));
    }
    
    $result = $stmt->execute([$hashedPassword, $email]);
    if (!$result) {
        throw new Exception("Failed to update password: " . implode(" ", $stmt->errorInfo()));
    }

    // Mark token as used
    $stmt = $pdo->prepare("UPDATE password_resets SET used = 1 WHERE token = ?");
    if (!$stmt) {
        throw new Exception("Failed to prepare token update query: " . implode(" ", $pdo->errorInfo()));
    }
    
    $result = $stmt->execute([$token]);
    if (!$result) {
        throw new Exception("Failed to mark token as used: " . implode(" ", $stmt->errorInfo()));
    }

    // Send confirmation email
    $mail = new PHPMailer(true);
    try {
        $mail->isSMTP();
        $mail->Host = 'smtp.hostinger.com';
        $mail->SMTPAuth = true;
        $mail->Username = '<EMAIL>';
        $mail->Password = 'Yash@400709';
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = 587;

        $mail->setFrom('<EMAIL>', 'Astra Logistics Manager');
        $mail->addAddress($email);
        $mail->isHTML(true);
        $mail->Subject = 'Password Reset Confirmation';
        $mail->Body = "
            <p>Hello,</p>
            <p>Your password has been successfully reset. If you did not request this change, please contact our support team immediately.</p>
            <p>Regards,<br>Astra Logistics Manager</p>
        ";

        $mail->send();
    } catch (Exception $e) {
        error_log("Password reset email failed: " . $mail->ErrorInfo);
    }

    echo json_encode([
        'success' => true,
        'message' => 'Password has been reset successfully. A confirmation email has been sent.'
    ]);

} catch (PDOException $e) {
    error_log("Database error in password reset: " . $e->getMessage() . "\n" . 
              "File: " . $e->getFile() . "\n" . 
              "Line: " . $e->getLine() . "\n" . 
              "Trace: " . $e->getTraceAsString());
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while resetting your password. Please try again.'
    ]);
} catch (Exception $e) {
    error_log("General error in password reset: " . $e->getMessage() . "\n" . 
              "File: " . $e->getFile() . "\n" . 
              "Line: " . $e->getLine() . "\n" . 
              "Trace: " . $e->getTraceAsString());
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while resetting your password. Please try again.'
    ]);
}
