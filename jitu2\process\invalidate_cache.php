<?php
/**
 * Cache Invalidation Endpoint
 * Called when data changes to notify clients to refresh their cache
 */

session_start();
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    echo json_encode(['success' => false, 'error' => 'Not logged in']);
    exit;
}

$action = $_POST['action'] ?? $_GET['action'] ?? '';
$username = $_SESSION['username'];

try {
    switch ($action) {
        case 'cn_entry_added':
        case 'cn_entry_deleted':
        case 'transaction_added':
        case 'transaction_deleted':
            // These actions affect CN numbers availability
            echo json_encode([
                'success' => true,
                'invalidate' => ['cash_entry', 'credit_entry'],
                'message' => 'CN numbers cache invalidated'
            ]);
            break;
            
        case 'customer_added':
        case 'customer_updated':
        case 'customer_deleted':
            // These actions affect customer list
            echo json_encode([
                'success' => true,
                'invalidate' => ['credit_entry'],
                'message' => 'Customer cache invalidated'
            ]);
            break;
            
        case 'settings_updated':
            // Settings changes affect both pages
            echo json_encode([
                'success' => true,
                'invalidate' => ['cash_entry', 'credit_entry'],
                'message' => 'Settings cache invalidated'
            ]);
            break;
            
        case 'clear_all':
            // Clear all caches
            echo json_encode([
                'success' => true,
                'invalidate' => ['all'],
                'message' => 'All caches invalidated'
            ]);
            break;
            
        default:
            echo json_encode([
                'success' => false,
                'error' => 'Invalid action'
            ]);
            break;
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Server error: ' . $e->getMessage()
    ]);
}
?>
