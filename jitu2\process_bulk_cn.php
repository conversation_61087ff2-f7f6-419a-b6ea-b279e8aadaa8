<?php
session_start();
require 'db_connect.php';
require 'vendor/autoload.php'; // For PhpSpreadsheet

use PhpOffice\PhpSpreadsheet\IOFactory;

if (!isset($_SESSION['username'])) {
    die("<script>alert('Error: You must be logged in.'); window.location.href='login.php';</script>");
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_FILES['excel_file'])) {
    die("<script>alert('Invalid request.'); window.location.href='dashboard.php?page=cn-entry';</script>");
}

$username = $_SESSION['username'];
$uploadedFile = $_FILES['excel_file'];

// Validate file
$allowedTypes = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    'application/vnd.ms-excel' // .xls
];

if (!in_array($uploadedFile['type'], $allowedTypes)) {
    die("<script>alert('Invalid file type. Please upload an Excel file.'); window.location.href='index.php?page=cn-entry';</script>");
}

try {
    $spreadsheet = IOFactory::load($uploadedFile['tmp_name']);
    $worksheet = $spreadsheet->getActiveSheet();
    $rows = $worksheet->toArray();

    // Skip header row and validate headers
    $headers = array_shift($rows);
    $expectedHeaders = ['CN No.', 'CN Date', 'CN Type'];
    if (array_diff($expectedHeaders, $headers)) {
        throw new Exception('Invalid Excel format. Please use the provided template.');
    }

    // Prepare insert statement
    $stmt = $conn->prepare("INSERT INTO cn_entries (cn_number, cn_date, cn_expiry_date, cn_type, username) VALUES (?, ?, ?, ?, ?)");

    $successCount = 0;
    $errors = [];

    foreach ($rows as $index => $row) {
        if (empty($row[0])) continue; // Skip empty rows

        $cnNumber = trim($row[0]);
        $cnDate = date('Y-m-d', strtotime($row[1]));
        $cnType = trim($row[2]);

        // Validate data
        if (empty($cnNumber) || empty($cnDate) || empty($cnType)) {
            $errors[] = "Row " . ($index + 2) . ": Missing required data";
            continue;
        }

        if (!in_array($cnType, ['Physical', 'Virtual'])) {
            $errors[] = "Row " . ($index + 2) . ": Invalid CN Type. Must be 'Physical' or 'Virtual'";
            continue;
        }

        // Calculate expiry date (CN Date + 180 days)
        $expiryDate = date('Y-m-d', strtotime($cnDate . ' + 180 days'));

        // Insert data
        $stmt->bind_param("sssss", $cnNumber, $cnDate, $expiryDate, $cnType, $username);
        
        if ($stmt->execute()) {
            $successCount++;
        } else {
            $errors[] = "Row " . ($index + 2) . ": " . $stmt->error;
        }
    }

    $stmt->close();
    $conn->close();

    // Prepare result message
    $message = "Successfully imported $successCount CN entries.";
    if (!empty($errors)) {
        $message .= "\n\nErrors:\n" . implode("\n", $errors);
    }

    echo "<script>
        alert('" . addslashes($message) . "');
        window.location.href='index.php?page=cn-entry';
    </script>";

} catch (Exception $e) {
    die("<script>alert('Error processing file: " . addslashes($e->getMessage()) . "'); window.location.href='index.php?page=cn-entry';</script>");
}
?> 