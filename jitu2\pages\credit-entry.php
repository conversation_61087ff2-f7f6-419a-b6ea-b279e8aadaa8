<?php
// Credit Entry Page
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Credit Entry</title>
    <!-- Add jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Add Cache Manager -->
    <script src="js/cache-manager.js"></script>
    <style>
        :root {
            --primary-blue: #2196F3;
            --light-blue: #E3F2FD;
            --hover-blue: #1976D2;
            --sky-blue: #87CEEB;
            --text-dark: #2c3e50;
            --border-color: #e0e0e0;
            --background: #F8FAFC;
        }

        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            background: var(--background);
            font-family: system-ui, -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        .content-wrapper {
            display: flex;
            gap: 2rem;
            max-width: 1100px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .content-box {
            flex: 1;
            max-width: 800px;
            padding: 1.5rem 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            font-size: 16px;
            margin: 0;
        }



        .form-container {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            max-width: 600px;
            margin: 0 auto;
        }

        .form-group {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 0;
        }

        .form-group label {
            width: 120px;
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-dark);
            margin-bottom: 0;
            flex-shrink: 0;
        }

        .form-group input,
        .form-group select {
            flex: 1;
            height: 32px;
            padding: 0.25rem 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 0.9rem;
            color: var(--text-dark);
            background: var(--background);
            transition: all 0.3s ease;
            max-width: 300px;
        }

        .form-group input[readonly] {
            background-color: #f5f5f5;
            cursor: not-allowed;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary-blue);
            background: white;
            box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
        }

        .button-container {
            margin-top: 1rem;
            text-align: center;
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        button {
            width: auto;
            min-width: 120px;
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
            font-weight: 500;
            background-color: var(--primary-blue);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        button:hover {
            background-color: var(--hover-blue);
            transform: translateY(-1px);
        }

        button:active {
            transform: translateY(0);
        }

        #verify_btn {
            background-color: #28a745;
            margin-right: 10px;
        }

        #verify_btn:hover {
            background-color: #218838;
        }

        #verify_btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .upload-section {
            width: 350px;
            padding: 1.5rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            margin: 0;
        }

        .upload-section h3 {
            color: var(--primary-blue);
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0 0 1rem 0;
            text-align: center;
        }

        .upload-section .form-group {
            flex-direction: column;
            align-items: stretch;
        }

        .upload-section label {
            margin-bottom: 0.5rem;
        }

        input[type="file"] {
            padding: 0.5rem;
            border: 2px dashed var(--border-color);
            border-radius: 6px;
            background: var(--background);
            cursor: pointer;
            font-size: 0.9rem;
        }

        .download-link {
            text-align: center;
            margin: 1rem 0;
        }

        .download-link a {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 0.5rem 1rem;
            text-decoration: none;
            font-size: 0.9rem;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        @media (max-width: 1024px) {
            .content-wrapper {
                flex-direction: column;
            }

            .upload-section {
                width: auto;
                max-width: 800px;
                margin: 0 auto;
            }
        }

        @media (max-width: 768px) {
            .content-wrapper {
                margin: 1rem;
                gap: 1rem;
            }

            .content-box {
                padding: 1rem;
            }

            .form-header {
                width: 90%;
                flex-wrap: wrap;
                gap: 0.75rem;
                justify-content: center;
            }

            .form-group {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.25rem;
            }

            .form-group label {
                width: 100%;
            }

            .form-group input,
            .form-group select {
                width: 100%;
                max-width: none;
            }
        }

        /* Loading overlay styles */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid var(--primary-blue);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        #mainContent {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease-in;
        }

        /* Header Section Styles */
        .form-header {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            margin: 0 auto 1.5rem auto;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
            width: 49%;
        }

        .form-title {
            color: var(--primary-blue);
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
        }

        /* Toggle Switch Styles */
        .toggle-container {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            flex-shrink: 0;
        }

        .toggle-label {
            font-size: 0.85rem;
            font-weight: 500;
            color: var(--text-dark);
            margin: 0;
        }

        .toggle-switch {
            position: relative;
            width: 50px;
            height: 24px;
            background-color: #ccc;
            border-radius: 12px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .toggle-switch.active {
            background-color: var(--primary-blue);
        }

        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background-color: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .toggle-switch.active .toggle-slider {
            transform: translateX(26px);
        }



        /* Manual Amount Input Styles */
        .manual-rate-group {
            display: none;
        }

        .manual-rate-group.active {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 0;
        }

        .manual-rate-group.active label {
            width: 120px;
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-dark);
            margin-bottom: 0;
            flex-shrink: 0;
        }

        .manual-rate-group.active input[type="text"] {
            flex: 1;
            height: 32px;
            padding: 0.25rem 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 0.9rem;
            color: var(--text-dark);
            background: var(--background);
            transition: all 0.3s ease;
            max-width: 300px;
        }

        .manual-rate-group.active input[type="text"]:focus {
            outline: none;
            border-color: var(--primary-blue);
            background: white;
            box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Main content container -->
    <div id="mainContent">

<?php
// Include database connection for session validation only
include 'db_connect.php';

// Initialize empty variables - data will be loaded via AJAX
$cn_numbers = [];
$customer_names = [];
$regionSeries = '';
$docketInitials = [
    'surface' => '',
    'aircargo' => '',
    'premium' => '',
    'ptp' => '',
    'cod' => '',
    'international' => '',
    'ecom_express' => '',
    'ecom_surface' => ''
];
?>

<div class="content-wrapper">
    <div class="content-box">
        <!-- Form Header with Title and Toggle -->
        <div class="form-header">
            <h2 class="form-title">Credit Entry</h2><br
            <div class="toggle-container">
                <label class="toggle-label">Entry without Quote</label>
                <div class="toggle-switch" id="entryWithoutQuoteToggle" onclick="toggleEntryMode()">
                    <div class="toggle-slider"></div>
                </div>
            </div>
        </div>

        <form action="process/process_credit_entry.php" method="POST" autocomplete="off" onsubmit="return validateForm()">
            <div class="form-container">
                <div class="form-group">
                    <label for="customer">Customer:</label>
                    <input type="text" id="customer" name="customer" list="customer_list" required>
                    <datalist id="customer_list">
                        <option value="Loading..."></option>
                    </datalist>
                </div>
                <div class="form-group">
                    <label for="docket_no">Docket No.:</label>
                    <input type="text" id="docket_no" name="docket_no" list="cn_list" required onkeyup="this.value = this.value.toUpperCase(); updateModeOfTSP()">
                    <datalist id="cn_list">
                        <option value="Loading..."></option>
                    </datalist>
                </div>
                <div class="form-group">
                    <label for="docket_date">Docket Date:</label>
                    <input type="date" id="docket_date" name="docket_date" required value="<?php echo date('Y-m-d'); ?>">
                </div>
                <div class="form-group">
                    <label for="pincode">Pincode:</label>
                    <input type="text" id="pincode" name="pincode" required onkeyup="fetchCity()" maxlength="6" onkeypress="return event.charCode >= 48 && event.charCode <= 57">
                </div>
                <div class="form-group">
                    <label for="destination">Destination:</label>
                    <input type="text" id="destination" name="destination" readonly>
                </div>
                <div class="form-group">
                    <label for="weight">Weight:</label>
                    <input type="text" id="weight" name="weight" required>
                </div>
                <div class="form-group">
                    <label for="mode_of_tsp">Mode of TSP:</label>
                    <select id="mode_of_tsp" name="mode_of_tsp" required>
                        <option value="">-- Select --</option>
                        <option value="Express">Express</option>
                        <option value="Surface">Surface</option>
                        <option value="Air Cargo">Air Cargo</option>
                        <option value="Premium">Premium</option>
                        <option value="PTP">PTP</option>
                        <option value="COD">COD</option>
                        <option value="International">International</option>
                        <option value="E-Com Express">E-Com Express</option>
                        <option value="E-Com Surface">E-Com Surface</option>
                    </select>
                </div>

                <!-- Manual Amount Input (Hidden by default) -->
                <div class="form-group manual-rate-group" id="manualRateGroup">
                    <label for="manual_amount">Amount:</label>
                    <input type="text" id="manual_amount" name="manual_amount">
                    <input type="hidden" id="entry_without_quote" name="entry_without_quote" value="0">
                </div>
                <div class="form-group">
                    <label for="waybill_value">Waybill Value:</label>
                    <input type="text" id="waybill_value" name="waybill_value">
                </div>
                <div class="form-group">
                    <label for="oda_charges">ODA Charges:</label>
                    <input type="text" id="oda_charges" name="oda_charges">
                </div>
                <div class="form-group">
                    <label for="risk_charges">Risk Charges:</label>
                    <select id="risk_charges" name="risk_charges" onchange="calculateRiskCharges()">
                        <option value="No Risk">No Risk</option>
                        <option value="Owner Risk">Owner Risk</option>
                        <option value="Carrier Risk">Carrier Risk</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="risk_charges_value">Risk Charges Value:</label>
                    <input type="text" id="risk_charges_value" name="risk_charges_value" readonly>
                </div>
                <div class="form-group">
                    <label for="remarks">Remarks:</label>
                    <input type="text" id="remarks" name="remarks">
                </div>
                <div class="button-container">
                    <button type="submit">Submit</button>
                </div>
            </div>
        </form>
    </div>

    <div class="upload-section">
        <h3>Bulk Excel Upload</h3>
        <form action="process/process_credit_entry.php" method="POST" enctype="multipart/form-data" onsubmit="return validateBulkUpload()">
            <div class="form-group">
                <label for="excel_file">Upload Excel File:</label>
                <input type="file" id="excel_file" name="excel_file" accept=".xlsx, .xls" required>
            </div>
            <div class="button-container">
                <button type="button" id="verify_btn" onclick="verifyBulkData()">Verify</button>
                <button type="submit">Upload</button>
            </div>
        </form>
        <div class="download-link">
            <a href="/upload/Credit_Data_Upload.xlsx" download>Download Template</a>
        </div>
    </div>
</div>

<script>
function fetchCity() {
    let pincodeInput = document.getElementById("pincode");
    let destinationInput = document.getElementById("destination");
    let weightInput = document.getElementById("weight"); 
    let pincode = pincodeInput.value.trim();

    if (pincode.length !== 6 || isNaN(pincode)) {
        destinationInput.value = "";
        return;
    }

    let xhr = new XMLHttpRequest();
    xhr.open("GET", "get_city.php?pincode=" + pincode, true);
    xhr.onreadystatechange = function () {
        if (xhr.readyState === 4 && xhr.status === 200) {
            let city = xhr.responseText.trim();
            destinationInput.value = (city !== "Not Found") ? city : "";

            if (city !== "Not Found") {
                weightInput.focus();
            }
        }
    };
    xhr.send();
}
</script>

<script>
function updateModeOfTSP() {
    let docketNo = document.getElementById("docket_no").value.toUpperCase();
    let modeOfTSP = document.getElementById("mode_of_tsp");

    // Check if entry without quote is enabled
    const entryWithoutQuote = document.getElementById("entry_without_quote").value === "1";

    modeOfTSP.innerHTML = "";

    // If entry without quote is enabled, show all modes
    if (entryWithoutQuote) {
        modeOfTSP.innerHTML = `
            <option value="">-- Select --</option>
            <option value="Express">Express</option>
            <option value="Surface">Surface</option>
            <option value="Air Cargo">Air Cargo</option>
            <option value="Premium">Premium</option>
            <option value="PTP">PTP</option>
            <option value="COD">COD</option>
            <option value="International">International</option>
            <option value="E-Com Express">E-Com Express</option>
            <option value="E-Com Surface">E-Com Surface</option>
        `;
        return;
    }

    // Use globally loaded data for normal mode
    if (docketNo.startsWith(regionSeries)) {
        modeOfTSP.innerHTML = '<option value="Express">Express</option>';
    } else if (docketNo.startsWith(docketInitials.surface)) {
        modeOfTSP.innerHTML = '<option value="Surface">Surface</option><option value="Air Cargo">Air Cargo</option>';
    } else if (docketNo.startsWith(docketInitials.premium)) {
        modeOfTSP.innerHTML = '<option value="Premium">Premium</option>';
    } else if (docketNo.startsWith(docketInitials.ptp)) {
        modeOfTSP.innerHTML = '<option value="PTP">PTP</option>';
    } else if (docketNo.startsWith(docketInitials.cod)) {
        modeOfTSP.innerHTML = '<option value="COD">COD</option>';
    } else if (docketNo.startsWith(docketInitials.international)) {
        modeOfTSP.innerHTML = '<option value="International">International</option>';
    } else if (docketNo.startsWith(docketInitials.ecom_express)) {
        modeOfTSP.innerHTML = '<option value="E-Com Express">E-Com Express</option>';
    } else if (docketNo.startsWith(docketInitials.ecom_surface)) {
        modeOfTSP.innerHTML = '<option value="E-Com Surface">E-Com Surface</option>';
    } else {
        modeOfTSP.innerHTML = `
            <option value="">-- Select --</option>
            <option value="Express">Express</option>
            <option value="Surface">Surface</option>
            <option value="Air Cargo">Air Cargo</option>
            <option value="Premium">Premium</option>
            <option value="PTP">PTP</option>
            <option value="COD">COD</option>
            <option value="International">International</option>
            <option value="E-Com Express">E-Com Express</option>
            <option value="E-Com Surface">E-Com Surface</option>
        `;
    }
}

// Toggle Entry Mode Function
function toggleEntryMode() {
    const toggle = document.getElementById("entryWithoutQuoteToggle");
    const manualRateGroup = document.getElementById("manualRateGroup");
    const entryWithoutQuoteInput = document.getElementById("entry_without_quote");
    const manualAmountInput = document.getElementById("manual_amount");

    // Toggle the switch
    toggle.classList.toggle("active");

    if (toggle.classList.contains("active")) {
        // Enable entry without quote mode
        manualRateGroup.classList.add("active");
        entryWithoutQuoteInput.value = "1";
        manualAmountInput.required = true;

        // Update mode of TSP to show all options
        updateModeOfTSP();

        // Focus on manual amount input
        setTimeout(() => {
            manualAmountInput.focus();
        }, 100);

    } else {
        // Disable entry without quote mode
        manualRateGroup.classList.remove("active");
        entryWithoutQuoteInput.value = "0";
        manualAmountInput.required = false;
        manualAmountInput.value = "";

        // Update mode of TSP to normal behavior
        updateModeOfTSP();
    }
}

// Form validation function
function validateForm() {
    const entryWithoutQuote = document.getElementById("entry_without_quote").value === "1";
    const manualAmount = document.getElementById("manual_amount").value;

    if (entryWithoutQuote && (!manualAmount || parseFloat(manualAmount) <= 0)) {
        alert("Please enter a valid amount when 'Entry without Quote' is enabled.");
        document.getElementById("manual_amount").focus();
        return false;
    }

    return true;
}
</script>




</div>

<script>
// Global variables for lazy loaded data
let regionSeries = '';
let docketInitials = {};

// Cache configuration using centralized cache manager
const CACHE_KEY = window.CacheManager.cacheKeys.CREDIT_ENTRY;

// Populate page with data
function populatePageData(data) {
    // Populate customer names datalist
    const customerList = document.getElementById('customer_list');
    customerList.innerHTML = '';

    if (data.customer_names && data.customer_names.length > 0) {
        data.customer_names.forEach(customer => {
            const option = document.createElement('option');
            option.value = customer;
            customerList.appendChild(option);
        });
    } else {
        const option = document.createElement('option');
        option.value = 'No Customers Available';
        customerList.appendChild(option);
    }

    // Populate CN numbers datalist
    const cnList = document.getElementById('cn_list');
    cnList.innerHTML = '';

    if (data.cn_numbers && data.cn_numbers.length > 0) {
        data.cn_numbers.forEach(cn => {
            const option = document.createElement('option');
            option.value = cn;
            cnList.appendChild(option);
        });
    } else {
        const option = document.createElement('option');
        option.value = 'No Available CN Numbers';
        cnList.appendChild(option);
    }

    // Store settings data globally
    regionSeries = data.settings.region_series || '';
    docketInitials = data.settings.docket_initials || {};
}

// Lazy load data function with caching
function loadPageData() {
    // Try to load from cache first
    const cachedData = window.CacheManager.get(CACHE_KEY);
    if (cachedData) {
        populatePageData(cachedData);
        return;
    }

    // If no cache, fetch from server
    fetch('process/get_page_data.php?type=credit')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Populate page
                populatePageData(data);

                // Save to cache for future use
                window.CacheManager.set(CACHE_KEY, data);
            }
        })
        .catch(error => {
            console.error('Error loading page data:', error);
            // Fallback - show error in datalists
            const customerList = document.getElementById('customer_list');
            const cnList = document.getElementById('cn_list');
            customerList.innerHTML = '<option value="Error loading data">Error loading data</option>';
            cnList.innerHTML = '<option value="Error loading data">Error loading data</option>';
        });
}

// Function to refresh cache manually
function refreshCache() {
    window.CacheManager.refresh(CACHE_KEY, loadPageData);
}

$(document).ready(function() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    const mainContent = document.getElementById('mainContent');

    // Ensure the loading overlay is visible initially
    loadingOverlay.style.visibility = 'visible';
    loadingOverlay.style.opacity = '1';

    // Hide main content initially
    mainContent.style.visibility = 'hidden';
    mainContent.style.opacity = '0';

    // Function to show content
    function showContent() {
        // First hide the loading overlay
        loadingOverlay.style.opacity = '0';

        // After a brief delay, show the main content
        setTimeout(() => {
            // Hide loading overlay completely
            loadingOverlay.style.visibility = 'hidden';

            // Show main content
            mainContent.style.visibility = 'visible';
            mainContent.style.opacity = '1';
            document.body.style.overflow = 'auto';

            // Load data via AJAX after page is visible
            loadPageData();

            // Initialize toggle state (disabled by default)
            const toggle = document.getElementById("entryWithoutQuoteToggle");
            const manualRateGroup = document.getElementById("manualRateGroup");
            const entryWithoutQuoteInput = document.getElementById("entry_without_quote");

            if (toggle && manualRateGroup && entryWithoutQuoteInput) {
                toggle.classList.remove("active");
                manualRateGroup.classList.remove("active");
                entryWithoutQuoteInput.value = "0";
                document.getElementById("manual_amount").required = false;
            }

            // Select the text in the first input field with a small delay
            setTimeout(() => {
                const customerInput = document.getElementById('customer');
                if (customerInput) {
                    customerInput.focus();
                    customerInput.select();
                }
            }, 100);
        }, 300);
    }

    // Wait for everything to load
    if (document.readyState === 'complete') {
        showContent();
    } else {
        window.addEventListener('load', showContent);
    }
});
</script>

<script>
function validateDocketCustomer() {
    const docketNo = document.getElementById("docket_no").value;
    const customer = document.getElementById("customer").value;

    if (docketNo && customer) {
        fetch('process/validate_docket_customer.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `docket_no=${encodeURIComponent(docketNo)}&customer=${encodeURIComponent(customer)}`
        })
        .then(response => response.json())
        .then(data => {
            if (!data.valid && data.error) {
                alert(data.error);
                document.getElementById("docket_no").value = '';
                document.getElementById("docket_no").focus();
            }
        })
        .catch(error => {
            console.error('Validation error:', error);
        });
    }
}

// Add event listeners
document.addEventListener('DOMContentLoaded', function() {
    const docketInput = document.getElementById("docket_no");
    const customerInput = document.getElementById("customer");

    if (docketInput) {
        docketInput.addEventListener('change', validateDocketCustomer);
    }
    if (customerInput) {
        customerInput.addEventListener('change', validateDocketCustomer);
    }
});
</script>

</div>

<script>
function calculateRiskCharges() {
    const waybillValue = parseFloat(document.getElementById('waybill_value').value) || 0;
    const riskType = document.getElementById('risk_charges').value;
    const riskChargesInput = document.getElementById('risk_charges_value');
    const customer = document.getElementById('customer').value;

    // If no customer selected or no waybill value, clear the risk charges
    if (!customer || !waybillValue) {
        riskChargesInput.value = '';
        return;
    }

    // If no risk selected, clear the value
    if (riskType === 'No Risk') {
        riskChargesInput.value = '';
        return;
    }

    // Make AJAX call to calculate risk charges
    $.ajax({
        url: 'process/calculate_risk_charges.php',
        method: 'POST',
        data: {
            customer: customer,
            waybill_value: waybillValue,
            risk_type: riskType
        },
        success: function(response) {
            try {
                const data = typeof response === 'string' ? JSON.parse(response) : response;
                if (data.success) {
                    riskChargesInput.value = data.risk_value;
                } else {
                    riskChargesInput.value = '';
                }
            } catch (error) {
                riskChargesInput.value = '';
            }
        },
        error: function(xhr, status, error) {
            riskChargesInput.value = '';
        }
    });
}

// Add event listeners for both waybill value and risk charges changes
document.addEventListener('DOMContentLoaded', function() {
    const waybillInput = document.getElementById('waybill_value');
    const riskChargesSelect = document.getElementById('risk_charges');
    const customerInput = document.getElementById('customer');

    // Listen for customer selection changes
    if (customerInput) {
        customerInput.addEventListener('change', function() {
            calculateRiskCharges();
        });
    }

    // Listen for waybill value changes
    if (waybillInput) {
        waybillInput.addEventListener('input', function() {
            const waybillValue = parseFloat(this.value) || 0;

            // Auto-select Owner Risk if waybill value exceeds 49999
            if (waybillValue > 49999) {
                const currentRiskType = document.getElementById('risk_charges').value;
                if (currentRiskType !== 'Owner Risk') {
                    document.getElementById('risk_charges').value = 'Owner Risk';
                    calculateRiskCharges();
                }
            }

            calculateRiskCharges();
        });
    }

    // Listen for risk charges dropdown changes
    if (riskChargesSelect) {
        riskChargesSelect.addEventListener('change', function() {
            const waybillValue = parseFloat(document.getElementById('waybill_value').value) || 0;
            const selectedRiskType = this.value;

            // If waybill value exceeds 49999 and user tries to select Carrier Risk
            if (waybillValue > 49999 && selectedRiskType === 'Carrier Risk') {
                if (confirm('Waybill value exceeds 49999. Owner Risk is recommended. Do you want to continue with Carrier Risk?')) {
                    calculateRiskCharges();
                } else {
                    this.value = 'Owner Risk';
                    calculateRiskCharges();
                }
            } else {
                calculateRiskCharges();
            }
        });
    }
});
</script>

</div>

<script>
function validateBulkUpload() {
    const fileInput = document.getElementById('excel_file');
    const file = fileInput.files[0];

    if (!file) {
        alert('Please select an Excel file to upload');
        return false;
    }

    return true;
}

function verifyBulkData() {
    const fileInput = document.getElementById('excel_file');
    const file = fileInput.files[0];

    if (!file) {
        alert('Please select an Excel file to verify');
        return false;
    }

    // Validate file type
    const fileName = file.name.toLowerCase();
    if (!fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {
        alert('Please select a valid Excel file (.xlsx or .xls)');
        return false;
    }

    // Create a temporary form to submit the file
    const tempForm = document.createElement('form');
    tempForm.method = 'POST';
    tempForm.action = 'process/verify_bulk_data.php';
    tempForm.target = '_blank';
    tempForm.enctype = 'multipart/form-data';
    tempForm.style.display = 'none';

    // Clone the file input
    const fileInputClone = fileInput.cloneNode(true);
    fileInputClone.name = 'excel_file';
    tempForm.appendChild(fileInputClone);

    // Add action input
    const actionInput = document.createElement('input');
    actionInput.type = 'hidden';
    actionInput.name = 'action';
    actionInput.value = 'verify';
    tempForm.appendChild(actionInput);

    // Add form to document, submit, then remove
    document.body.appendChild(tempForm);
    tempForm.submit();
    document.body.removeChild(tempForm);

    // Show feedback to user
    const verifyBtn = document.getElementById('verify_btn');
    const originalText = verifyBtn.textContent;
    verifyBtn.textContent = 'Opening Results...';
    verifyBtn.disabled = true;

    // Re-enable button after a short delay
    setTimeout(() => {
        verifyBtn.textContent = originalText;
        verifyBtn.disabled = false;
    }, 2000);
}


</script>

</div>
</body>
</html>