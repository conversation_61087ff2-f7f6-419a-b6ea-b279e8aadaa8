<?php
session_start();

// Check if user is logged in
if (isset($_SESSION['user_id'])) {
    // Include header first (contains session and database logic)
    include 'header.php';

    // Get the requested page
    $page = isset($_GET['page']) ? str_replace('%2F', '/', $_GET['page']) : 'dashboard';

    // Include the rest of the layout
    include 'sidebar.php';
    include 'main-content.php';

    // Include the requested page content
    //include($page.'.php');

    // Include footer last
    //include 'footer.php';
} else {
    // Show landing page for non-logged-in users
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Astra Logistics Manager - Your Partner in Logistics Success</title>
        <link rel="icon" type="image/x-icon" href="images/favicon.ico" />  
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
        <style>
            :root {
                --primary-color: #4f46e5;
                --secondary-color: #7c3aed;
                --accent-color: #06b6d4;
                --success-color: #10b981;
                --warning-color: #f59e0b;
                --danger-color: #ef4444;
                --background-color: #ffffff;
                --text-color: #1f2937;
                --text-muted: #64748b;
                --border-color: rgba(0, 0, 0, 0.1);
                --hover-bg: rgba(79, 70, 229, 0.08);
                --glass-bg: rgba(255, 255, 255, 0.98);
                --glass-border: rgba(255, 255, 255, 0.2);
                --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            }

            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'Inter', system-ui, -apple-system, sans-serif;
                line-height: 1.5;
                color: var(--text-color);
                background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            }

            .hero {
                min-height: 100vh;
                height: 100vh;
                display: flex;
                flex-direction: column;
                position: relative;
                overflow: hidden;
            }

            .hero::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 100%;
                background: linear-gradient(135deg, rgba(79, 70, 229, 0.1) 0%, rgba(124, 58, 237, 0.1) 100%);
                clip-path: polygon(0 0, 100% 0, 100% 85%, 0% 100%);
                z-index: -1;
            }

            .navbar {
                padding: 0.75rem 2rem;
                display: flex;
                justify-content: space-between;
                align-items: center;
                background: var(--glass-bg);
                backdrop-filter: blur(12px);
                -webkit-backdrop-filter: blur(12px);
                border-bottom: 1px solid var(--glass-border);
            }

            .brand {
                display: flex;
                align-items: center;
                gap: 1rem;
                text-decoration: none;
            }

            .brand i {
                font-size: 2rem;
                background: var(--gradient-primary);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }

            .brand-text {
                display: flex;
                flex-direction: column;
            }

            .brand-name {
                font-size: 1.4rem;
                font-weight: 800;
                color: var(--text-color);
                background: var(--gradient-primary);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }

            .brand-tagline {
                font-size: 0.8rem;
                color: var(--text-muted);
            }

            .auth-buttons {
                display: flex;
                gap: 1rem;
            }

            .btn {
                padding: 0.75rem 1.5rem;
                border-radius: 8px;
                font-weight: 600;
                text-decoration: none;
                transition: all 0.3s ease;
                font-size: 0.9rem;
            }

            .btn-login {
                color: var(--primary-color);
                background: var(--hover-bg);
            }

            .btn-register {
                color: white;
                background: var(--gradient-primary);
            }

            .btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(79, 70, 229, 0.2);
            }

            .hero-content {
                max-width: 1200px;
                margin: 0 auto;
                padding: 1.5rem 2rem 0.5rem;
                text-align: center;
            }

            .hero-title {
                font-size: 2.5rem;
                font-weight: 800;
                margin-bottom: 0.5rem;
                background: var(--gradient-primary);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                line-height: 1.2;
            }

            .hero-subtitle {
                font-size: 1rem;
                color: var(--text-muted);
                max-width: 600px;
                margin: 0 auto 1rem;
            }

            .services {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 1rem;
                padding: 0 2rem;
                max-width: 1200px;
                margin: 0 auto;
                flex: 1;
                align-content: start;
            }

            .service-card {
                background: var(--glass-bg);
                backdrop-filter: blur(12px);
                -webkit-backdrop-filter: blur(12px);
                border: 1px solid var(--glass-border);
                border-radius: 12px;
                padding: 1rem;
                transition: all 0.3s ease;
                display: flex;
                flex-direction: column;
                gap: 0.5rem;
                height: min-content;
            }

            .service-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 8px 24px rgba(79, 70, 229, 0.15);
            }

            .service-icon {
                width: 40px;
                height: 40px;
                border-radius: 10px;
                background: var(--hover-bg);
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .service-icon i {
                font-size: 1.25rem;
                color: var(--primary-color);
            }

            .service-title {
                font-size: 1rem;
                font-weight: 600;
                color: var(--text-color);
            }

            .service-description {
                font-size: 0.85rem;
                color: var(--text-muted);
                line-height: 1.4;
            }

            /* Modal Styles */
            .modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                backdrop-filter: blur(8px);
                display: none;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            .modal-overlay.active {
                display: flex;
                opacity: 1;
            }

            .login-modal {
                background: var(--glass-bg);
                backdrop-filter: blur(16px);
                -webkit-backdrop-filter: blur(16px);
                border: 1px solid var(--glass-border);
                border-radius: 16px;
                padding: 2rem;
                width: 100%;
                max-width: 400px;
                position: relative;
                transform: translateY(-20px);
                transition: transform 0.3s ease;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            }

            .modal-overlay.active .login-modal {
                transform: translateY(0);
            }

            .login-header {
                text-align: center;
                margin-bottom: 1.5rem;
            }

            .login-title {
                font-size: 1.5rem;
                font-weight: 700;
                color: var(--text-color);
                margin-bottom: 0.5rem;
            }

            .login-subtitle {
                font-size: 0.9rem;
                color: var(--text-muted);
            }

            .close-modal {
                position: absolute;
                top: 1rem;
                right: 1rem;
                background: none;
                border: none;
                color: var(--text-muted);
                cursor: pointer;
                padding: 0.5rem;
                border-radius: 8px;
                transition: all 0.3s ease;
            }

            .close-modal:hover {
                background: var(--hover-bg);
                color: var(--primary-color);
            }

            .login-form {
                display: flex;
                flex-direction: column;
                gap: 1rem;
            }

            .form-group {
                display: flex;
                flex-direction: column;
                gap: 0.5rem;
            }

            .form-group label {
                font-size: 0.9rem;
                font-weight: 500;
                color: var(--text-color);
            }

            .form-group input {
                padding: 0.75rem 1rem;
                border: 1px solid var(--border-color);
                border-radius: 8px;
                font-size: 0.9rem;
                transition: all 0.3s ease;
                background: rgba(255, 255, 255, 0.5);
            }

            .form-group input:focus {
                outline: none;
                border-color: var(--primary-color);
                box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
            }

            .remember-me {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-size: 0.9rem;
                color: var(--text-muted);
            }

            .submit-btn {
                background: var(--gradient-primary);
                color: white;
                border: none;
                padding: 0.75rem;
                border-radius: 8px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .submit-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(79, 70, 229, 0.2);
            }

            .login-footer {
                text-align: center;
                margin-top: 1rem;
                font-size: 0.9rem;
                color: var(--text-muted);
            }

            .login-footer a {
                color: var(--primary-color);
                text-decoration: none;
                font-weight: 500;
            }

            .login-footer a:hover {
                text-decoration: underline;
            }

            .alert {
                padding: 0.75rem 1rem;
                border-radius: 8px;
                margin-bottom: 1rem;
                font-size: 0.9rem;
                display: none;
            }

            .alert-error {
                background: rgba(239, 68, 68, 0.1);
                color: var(--danger-color);
                border: 1px solid rgba(239, 68, 68, 0.2);
            }

            .alert-success {
                background: rgba(16, 185, 129, 0.1);
                color: var(--success-color);
                border: 1px solid rgba(16, 185, 129, 0.2);
            }

            /* Responsive Styles */
            @media (max-width: 1024px) {
                .services {
                    grid-template-columns: repeat(2, 1fr);
                }
            }

            @media (max-width: 768px) {
                .navbar {
                    padding: 0.5rem 1rem;
                }

                .hero-content {
                    padding: 1rem 1rem 0.5rem;
                }

                .hero-title {
                    font-size: 1.75rem;
                }

                .hero-subtitle {
                    font-size: 0.9rem;
                }

                .services {
                    grid-template-columns: repeat(2, 1fr);
                    gap: 0.75rem;
                    padding: 0.5rem 1rem;
                }

                .service-card {
                    padding: 0.75rem;
                }

                .service-icon {
                    width: 36px;
                    height: 36px;
                }

                .service-icon i {
                    font-size: 1.1rem;
                }

                .service-title {
                    font-size: 0.9rem;
                }

                .service-description {
                    font-size: 0.8rem;
                }
            }

            @media (max-width: 480px) {
                .services {
                    grid-template-columns: 1fr;
                }
            }
        </style>
    </head>
    <body>
        <div class="hero">
            <nav class="navbar">
                <a href="#" class="brand">
                    <i class="fas fa-truck-loading"></i>
                    <div class="brand-text">
                        <span class="brand-name">Astra Logistics Manager</span>
                        <span class="brand-tagline">Your Partner in Logistics Success</span>
                    </div>
                </a>
                <div class="auth-buttons">
                    <a href="#" class="btn btn-login">Login</a>
                    <a href="register.php" class="btn btn-register">Register</a>
                </div>
            </nav>

            <div class="hero-content">
                <h1 class="hero-title">Streamline Your Logistics Operations</h1>
                <p class="hero-subtitle">
                    Complete logistics management platform for franchisees. 
                    Manage bookings, track shipments, and handle billing with ease.
                </p>
            </div>

            <div class="services">
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <h3 class="service-title">Cash & Credit Bookings</h3>
                    <p class="service-description">
                        Manage cash and credit bookings efficiently. Track pending cash records 
                        and maintain comprehensive booking reports.
                    </p>
                </div>

                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <h3 class="service-title">C-Note Management</h3>
                    <p class="service-description">
                        Create and manage C-Notes with ease. Generate detailed reports 
                        and track all consignment notes in real-time.
                    </p>
                </div>

                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <h3 class="service-title">Freight Rate Calculator</h3>
                    <p class="service-description">
                        Instantly calculate freight rates. Set customer pricing and 
                        manage service provider rates efficiently.
                    </p>
                </div>

                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3 class="service-title">Reports & Analytics</h3>
                    <p class="service-description">
                        Track daily operations, analyze missing data, and access 
                        comprehensive billing vs TS reports.
                    </p>
                </div>

                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="service-title">Customer Management</h3>
                    <p class="service-description">
                        Manage customer profiles, track payment history, and maintain 
                        detailed billing records efficiently.
                    </p>
                </div>

                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-file-invoice-dollar"></i>
                    </div>
                    <h3 class="service-title">Invoice & Billing</h3>
                    <p class="service-description">
                        Generate invoices, track payments, and manage outstanding 
                        bills with our comprehensive billing system.
                    </p>
                </div>
            </div>
        </div>

        <!-- Login Modal -->
        <div class="modal-overlay" id="loginModal">
            <div class="login-modal">
                <button class="close-modal" onclick="closeLoginModal()">
                    <i class="fas fa-times"></i>
                </button>
                <div class="login-header">
                    <h2 class="login-title">Welcome Back</h2>
                    <p class="login-subtitle">Log in to your account to continue</p>
                </div>
                <div class="alert alert-error" id="loginAlert"></div>
                <form class="login-form" id="loginForm" onsubmit="handleLogin(event)">
                    <div class="form-group">
                        <label for="username">Username</label>
                        <input type="text" id="username" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    <div class="remember-me">
                        <input type="checkbox" id="remember" name="remember">
                        <label for="remember">Remember me</label>
                        <a href="#" class="forgot-password" onclick="openForgotPasswordModal(event)">Forgot Password?</a>
                    </div>
                    <button type="submit" class="submit-btn">
                        <i class="fas fa-sign-in-alt"></i> Login
                    </button>
                </form>
                <div class="login-footer">
                    <p>Don't have an account? <a href="register.php">Register here</a></p>
                </div>
            </div>
        </div>

        <!-- Forgot Password Modal -->
        <div class="modal-overlay" id="forgotPasswordModal">
            <div class="login-modal">
                <button class="close-modal" onclick="closeForgotPasswordModal()">
                    <i class="fas fa-times"></i>
                </button>
                <div class="login-header">
                    <h2 class="login-title">Reset Password</h2>
                    <p class="login-subtitle">Enter your email to receive password reset instructions</p>
                </div>
                <div class="alert alert-error" id="forgotPasswordAlert"></div>
                <form class="login-form" id="forgotPasswordForm" onsubmit="handleForgotPassword(event)">
                    <div class="form-group">
                        <label for="resetEmail">Email Address</label>
                        <input type="email" id="resetEmail" name="email" required>
                    </div>
                    <button type="submit" class="submit-btn">
                        <i class="fas fa-paper-plane"></i> Send Reset Link
                    </button>
                </form>
                <div class="login-footer">
                    <p>Remember your password? <a href="#" onclick="switchToLoginModal(event)">Back to Login</a></p>
                </div>
            </div>
        </div>

        <script>
            // Update the login button to open modal instead of redirecting
            document.querySelector('.btn-login').addEventListener('click', function(e) {
                e.preventDefault();
                openLoginModal();
            });

            function openLoginModal() {
                const modal = document.getElementById('loginModal');
                modal.classList.add('active');
                document.body.style.overflow = 'hidden';
            }

            function closeLoginModal() {
                const modal = document.getElementById('loginModal');
                modal.classList.remove('active');
                document.body.style.overflow = '';
                // Clear form and alerts
                document.getElementById('loginForm').reset();
                document.getElementById('loginAlert').style.display = 'none';
            }

            // Close modal when clicking outside
            document.getElementById('loginModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeLoginModal();
                }
            });

            function handleLogin(event) {
                event.preventDefault();
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                const remember = document.getElementById('remember').checked;
                const alertEl = document.getElementById('loginAlert');

                // Show loading state
                const submitBtn = event.target.querySelector('.submit-btn');
                const originalBtnText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Logging in...';
                submitBtn.disabled = true;

                // Send login request
                fetch('login_process.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        remember: remember
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alertEl.className = 'alert alert-success';
                        alertEl.textContent = data.message;
                        alertEl.style.display = 'block';
                        setTimeout(() => {
                            window.location.href = data.redirect;
                        }, 1500);
                    } else {
                        alertEl.className = 'alert alert-error';
                        alertEl.textContent = data.message;
                        alertEl.style.display = 'block';
                        submitBtn.innerHTML = originalBtnText;
                        submitBtn.disabled = false;
                    }
                })
                .catch(error => {
                    alertEl.className = 'alert alert-error';
                    alertEl.textContent = 'An error occurred. Please try again.';
                    alertEl.style.display = 'block';
                    submitBtn.innerHTML = originalBtnText;
                    submitBtn.disabled = false;
                });
            }

            function openForgotPasswordModal(event) {
                event.preventDefault();
                closeLoginModal();
                const modal = document.getElementById('forgotPasswordModal');
                modal.classList.add('active');
                document.body.style.overflow = 'hidden';
            }

            function closeForgotPasswordModal() {
                const modal = document.getElementById('forgotPasswordModal');
                modal.classList.remove('active');
                document.body.style.overflow = '';
                document.getElementById('forgotPasswordForm').reset();
                document.getElementById('forgotPasswordAlert').style.display = 'none';
            }

            function switchToLoginModal(event) {
                event.preventDefault();
                closeForgotPasswordModal();
                openLoginModal();
            }

            // Close forgot password modal when clicking outside
            document.getElementById('forgotPasswordModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeForgotPasswordModal();
                }
            });

            function handleForgotPassword(event) {
                event.preventDefault();
                const email = document.getElementById('resetEmail').value;
                const alertEl = document.getElementById('forgotPasswordAlert');

                // Show loading state
                const submitBtn = event.target.querySelector('.submit-btn');
                const originalBtnText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
                submitBtn.disabled = true;

                // Send forgot password request
                fetch('forgot_password.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: email
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alertEl.className = 'alert alert-success';
                        alertEl.textContent = data.message;
                        alertEl.style.display = 'block';
                        document.getElementById('forgotPasswordForm').reset();
                        setTimeout(() => {
                            closeForgotPasswordModal();
                            openLoginModal();
                        }, 2000);
                    } else {
                        alertEl.className = 'alert alert-error';
                        alertEl.textContent = data.message;
                        alertEl.style.display = 'block';
                        submitBtn.innerHTML = originalBtnText;
                        submitBtn.disabled = false;
                    }
                })
                .catch(error => {
                    alertEl.className = 'alert alert-error';
                    alertEl.textContent = 'An error occurred. Please try again.';
                    alertEl.style.display = 'block';
                    submitBtn.innerHTML = originalBtnText;
                    submitBtn.disabled = false;
                });
            }
        </script>
        
    </body>
    </html>
    <?php
}
?>
