<!-- Sidebar Component -->
<style>
    :root {
        --primary-color: #6366f1;
        --secondary-color: #8b5cf6;
        --accent-color: #06b6d4;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --danger-color: #ef4444;
        --background-color: #ffffff;
        --text-color: #1f2937;
        --text-muted: #64748b;
        --border-color: rgba(0, 0, 0, 0.1);
        --hover-bg: rgba(99, 102, 241, 0.08);
        --active-bg: rgba(99, 102, 241, 0.12);
        --sidebar-width: 280px;
        --header-height: 70px;
        --transition-speed: 0.3s;
        --glass-bg: rgba(255, 255, 255, 0.95);
        --glass-border: rgba(255, 255, 255, 0.2);
        --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
        --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    }

    .sidebar {
        position: fixed;
        left: 0;
        top: var(--header-height);
        height: calc(100vh - var(--header-height));
        width: var(--sidebar-width);
        background: var(--glass-bg);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-right: 1px solid var(--glass-border);
        box-shadow: var(--glass-shadow);
        overflow-y: auto;
        overflow-x: hidden;
        scrollbar-width: thin;
        scrollbar-color: var(--primary-color) transparent;
        scroll-behavior: smooth;
        z-index: 100;
        transition: all var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1);
        padding: 1rem 0;
    }

    .sidebar::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg,
            var(--primary-color) 0%,
            var(--secondary-color) 25%,
            var(--accent-color) 50%,
            var(--success-color) 75%,
            var(--warning-color) 100%
        );
        opacity: 0.8;
    }

    /* Webkit scrollbar styles */
    .sidebar::-webkit-scrollbar {
        width: 6px;
        background: transparent;
    }

    .sidebar::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.05);
        border-radius: 3px;
        margin: 2px;
    }

    .sidebar::-webkit-scrollbar-thumb {
        background: var(--primary-color);
        border-radius: 3px;
        transition: background 0.3s ease;
    }

    .sidebar::-webkit-scrollbar-thumb:hover {
        background: var(--secondary-color);
    }

    /* Firefox scrollbar styles */
    .sidebar {
        scrollbar-width: thin;
        scrollbar-color: var(--primary-color) transparent;
    }

    .menu {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .menu-section {
        padding: 0 1rem;
        margin-bottom: 1.5rem;
    }

    .menu-section:last-child {
        margin-bottom: 2rem;
    }

    .menu-section-title {
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        color: var(--text-muted);
        margin-bottom: 1rem;
        padding-left: 0.75rem;
        font-weight: 600;
        position: relative;
    }

    .menu-section-title::after {
        content: '';
        position: absolute;
        bottom: -4px;
        left: 0;
        width: 24px;
        height: 2px;
        background: var(--gradient-primary);
        border-radius: 2px;
    }

    .menu li {
        margin: 0.25rem 0;
    }

    .menu a {
        display: flex;
        align-items: center;
        padding: 0.875rem 1rem;
        color: var(--text-color);
        text-decoration: none;
        font-size: 0.9375rem;
        border-radius: 12px;
        transition: all var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        margin: 0.25rem 0.5rem;
        font-weight: 450;
    }

    .menu a::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        height: 70%;
        width: 3px;
        background: var(--gradient-primary);
        transform: translateY(-50%) scaleY(0);
        transition: transform var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1);
        border-radius: 0 2px 2px 0;
    }

    .menu a:hover {
        background: var(--hover-bg);
        color: var(--primary-color);
        transform: translateX(4px);
    }

    .menu a:hover::before {
        transform: translateY(-50%) scaleY(1);
    }

    .menu a.active {
        background: var(--active-bg);
        color: var(--primary-color);
        font-weight: 500;
        box-shadow: 0 2px 8px rgba(99, 102, 241, 0.1);
    }

    .menu a.active::before {
        transform: translateY(-50%) scaleY(1);
    }

    .menu a span {
        margin-right: 1rem;
        font-size: 1.25rem;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 1.5rem;
        transition: all var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1);
        color: var(--text-muted);
        opacity: 0.85;
    }

    .menu a:hover span,
    .menu a.active span {
        color: var(--primary-color);
        transform: scale(1.1);
        opacity: 1;
    }

    @media (max-width: 768px) {
        .sidebar {
            transform: translateX(-100%);
            box-shadow: none;
        }

        .sidebar.active {
            transform: translateX(0);
            box-shadow: var(--glass-shadow);
        }

        .main-content {
            margin-left: 0 !important;
        }
    }
</style>

<aside class="sidebar">
    <nav class="menu">
        <div class="menu-section">
            <div class="menu-section-title">Main Navigation</div>
            <ul class="menu">
                <?php
                $current_page = $_GET['page'] ?? 'dashboard';
                $menu_items = [
                    'main' => [
                        ['page' => 'dashboard', 'icon' => 'fas fa-chart-line', 'title' => 'Dashboard'],
                        ['page' => 'cash-entry', 'icon' => 'fas fa-money-bill-wave', 'title' => 'Cash Booking Entry'],
                        ['page' => 'credit-entry', 'icon' => 'fas fa-credit-card', 'title' => 'Credit Booking Entry'],
                        ['page' => 'pending_cash', 'icon' => 'fas fa-clock', 'title' => 'Pending Cash Records'],
                        ['page' => 'cash_report', 'icon' => 'fas fa-file-invoice-dollar', 'title' => 'Cash Report'],
                    ],
                    'reports' => [
                        ['page' => 'report', 'icon' => 'fas fa-file-alt', 'title' => 'Cash & Credit Report'],
                        ['page' => 'cn-entry', 'icon' => 'fas fa-file-invoice', 'title' => 'C-Note Entry'],
                        ['page' => 'available_cn_report', 'icon' => 'fas fa-clipboard-check', 'title' => 'C-Note Report'],
                        ['page' => 'freight_rate-calculator', 'icon' => 'fas fa-calculator', 'title' => 'Freight Rate Calculator'],
                        ['page' => 'booking_data', 'icon' => 'fas fa-database', 'title' => 'Missing Data Analysis'],
                        ['page' => 'tracking_report', 'icon' => 'fas fa-route', 'title' => 'Daily Tracking Report'],
                    ],
                    'management' => [
                        ['page' => 'customer_dashboard', 'icon' => 'fas fa-users', 'title' => 'Manage Customers'],
                        ['page' => 'inv_ts_report', 'icon' => 'fas fa-file-invoice-dollar', 'title' => 'Bill vs TS Report'],
                        ['page' => 'outstanding', 'icon' => 'fas fa-hand-holding-usd', 'title' => 'Billing & Payment Report'],
                        ['page' => 'rate_master', 'icon' => 'fas fa-tags', 'title' => 'Customer Pricing'],
                        ['page' => 'ts_rate_master', 'icon' => 'fas fa-tag', 'title' => 'Service Provider Rates'],
                        ['page' => 'invoice_dashboard', 'icon' => 'fas fa-file-invoice', 'title' => 'Invoice Management'],
                    ]
                ];

                foreach ($menu_items['main'] as $item) {
                    echo '<li>';
                    echo '<a href="index.php?page=' . $item['page'] . '"' .
                         ($current_page == $item['page'] ? ' class="active"' : '') . '>';
                    echo '<span><i class="' . $item['icon'] . '"></i></span>' . $item['title'];
                    echo '</a>';
                    echo '</li>';
                }
                ?>
            </ul>
        </div>

        <div class="menu-section">
            <div class="menu-section-title">Reports & Analysis</div>
            <ul class="menu">
                <?php
                foreach ($menu_items['reports'] as $item) {
                    echo '<li>';
                    echo '<a href="index.php?page=' . $item['page'] . '"' .
                         ($current_page == $item['page'] ? ' class="active"' : '') . '>';
                    echo '<span><i class="' . $item['icon'] . '"></i></span>' . $item['title'];
                    echo '</a>';
                    echo '</li>';
                }
                ?>
            </ul>
        </div>

        <div class="menu-section">
            <div class="menu-section-title">Management</div>
            <ul class="menu">
                <?php
                foreach ($menu_items['management'] as $item) {
                    echo '<li>';
                    echo '<a href="dashboard.php?page=' . $item['page'] . '"' .
                         ($current_page == $item['page'] ? ' class="active"' : '') . '>';
                    echo '<span><i class="' . $item['icon'] . '"></i></span>' . $item['title'];
                    echo '</a>';
                    echo '</li>';
                }
                ?>
            </ul>
        </div>

        <div class="menu-section">
            <div class="menu-section-title">MISC Tools</div>
            <ul class="menu">
                <li>
                    <a href="index.php?page=pincode" <?php echo ($current_page == 'pincode' ? ' class="active"' : ''); ?>>
                        <span><i class="fas fa-map-marker-alt"></i></span>Pincode Management
                    </a>
                </li>
                <li>
                    <a href="index.php?page=transaction_form.php" <?php echo ($current_page == 'transaction_form.php' ? ' class="active"' : ''); ?>>
                        <span><i class="fas fa-plus-circle"></i></span>Direct Manual Entry
                    </a>
                </li>
            </ul>
        </div>

        <!-- Transactions Viewer Section -->
        <div class="menu-section">
            <div class="menu-section-title">Data Tools</div>
            <ul class="menu">
                <li>
                    <a href="?page=transactions_view" class="<?php echo ($current_page === 'transactions_view' ? 'active' : ''); ?>">
                        <span class="fas fa-table"></span> Transactions Viewer
                    </a>
                </li>
            </ul>
        </div>
    </nav>
</aside>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const sidebar = document.querySelector('.sidebar');
        const menuItems = document.querySelectorAll('.menu a');

        // Add hover effect
        menuItems.forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateX(4px)';
            });

            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateX(0)';
            });
        });

        // Handle window resize
        let resizeTimer;
        window.addEventListener('resize', function() {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(function() {
                if (window.innerWidth > 768) {
                    sidebar.classList.remove('active');
                }
            }, 250);
        });
    });
</script>
