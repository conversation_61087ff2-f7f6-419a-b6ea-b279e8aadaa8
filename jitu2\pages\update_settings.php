<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'db_connect.php';  // Updated path since we're already in the correct directory

if (!isset($_SESSION['username'])) {
    header('Location: login.php');
    exit();
}

$username = $_SESSION['username'];

// Validate and sanitize inputs
$express_cn_cost = isset($_POST['express_cn_cost']) ? (float)$_POST['express_cn_cost'] : 0;
$aircargo_cn_cost = isset($_POST['aircargo_cn_cost']) ? (float)$_POST['aircargo_cn_cost'] : 0;
$surface_cn_cost = isset($_POST['surface_cn_cost']) ? (float)$_POST['surface_cn_cost'] : 0;
$premium_cn_cost = isset($_POST['premium_cn_cost']) ? (float)$_POST['premium_cn_cost'] : 0;
$region_series = isset($_POST['region_series']) ? htmlspecialchars($_POST['region_series'], ENT_QUOTES, 'UTF-8') : '';
$fsc = isset($_POST['fsc']) ? (float)$_POST['fsc'] : 0;
$gst = isset($_POST['gst']) ? (float)$_POST['gst'] : 0;
$metro_status = isset($_POST['metro_status']) && in_array($_POST['metro_status'], ['Metro', 'Non-Metro']) ? $_POST['metro_status'] : 'Non-Metro';
$billing_zone = isset($_POST['billing_zone']) ? htmlspecialchars($_POST['billing_zone'], ENT_QUOTES, 'UTF-8') : '';
$invoice_series = isset($_POST['invoice_series']) ? htmlspecialchars($_POST['invoice_series'], ENT_QUOTES, 'UTF-8') : '';
$invoice_number = isset($_POST['invoice_number']) ? htmlspecialchars($_POST['invoice_number'], ENT_QUOTES, 'UTF-8') : '';
$manual_series = isset($_POST['manual_series']) ? htmlspecialchars($_POST['manual_series'], ENT_QUOTES, 'UTF-8') : '';
$manual_invoice_number = isset($_POST['manual_invoice_number']) ? htmlspecialchars($_POST['manual_invoice_number'], ENT_QUOTES, 'UTF-8') : '0001';
$surface_docket = isset($_POST['surface_docket']) ? htmlspecialchars($_POST['surface_docket'], ENT_QUOTES, 'UTF-8') : '';
$aircargo_docket = isset($_POST['aircargo_docket']) ? htmlspecialchars($_POST['aircargo_docket'], ENT_QUOTES, 'UTF-8') : '';
$premium_docket = isset($_POST['premium_docket']) ? htmlspecialchars($_POST['premium_docket'], ENT_QUOTES, 'UTF-8') : '';
$ptp_docket = isset($_POST['ptp_docket']) ? htmlspecialchars($_POST['ptp_docket'], ENT_QUOTES, 'UTF-8') : '';
$cod_docket = isset($_POST['cod_docket']) ? htmlspecialchars($_POST['cod_docket'], ENT_QUOTES, 'UTF-8') : '';
$international_docket = isset($_POST['international_docket']) ? htmlspecialchars($_POST['international_docket'], ENT_QUOTES, 'UTF-8') : '';
$ecom_express_docket = isset($_POST['ecom_express_docket']) ? htmlspecialchars($_POST['ecom_express_docket'], ENT_QUOTES, 'UTF-8') : '';
$ecom_surface_docket = isset($_POST['ecom_surface_docket']) ? htmlspecialchars($_POST['ecom_surface_docket'], ENT_QUOTES, 'UTF-8') : '';
$cnote_validation_enabled = isset($_POST['cnote_validation_enabled']) ? 1 : 0;

try {
    // Check if settings exist for the user
    $check_stmt = $conn->prepare("SELECT id FROM settings WHERE username = ?");
    if (!$check_stmt) {
        throw new Exception("Prepare failed: " . $conn->error);
    }
    
    $check_stmt->bind_param("s", $username);
    if (!$check_stmt->execute()) {
        throw new Exception("Execute failed: " . $check_stmt->error);
    }
    
    $result = $check_stmt->get_result();

    if ($result->num_rows > 0) {
        // Update existing settings
        $stmt = $conn->prepare("UPDATE settings SET 
            express_cn_cost = ?,
            aircargo_cn_cost = ?,
            surface_cn_cost = ?,
            premium_cn_cost = ?,
            region_series = ?,
            fsc = ?,
            gst = ?,
            metro_status = ?,
            billing_zone = ?,
            invoice_series = ?,
            invoice_number = ?,
            manual_series = ?,
            manual_invoice_number = ?,
            surface_docket = ?,
            aircargo_docket = ?,
            premium_docket = ?,
            ptp_docket = ?,
            cod_docket = ?,
            international_docket = ?,
            ecom_express_docket = ?,
            ecom_surface_docket = ?,
            cnote_validation_enabled = ?
            WHERE username = ?");
        
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $conn->error);
        }
        
        $stmt->bind_param(
            "ddddsddssssssssssssssis",
            $express_cn_cost,
            $aircargo_cn_cost,
            $surface_cn_cost,
            $premium_cn_cost,
            $region_series,
            $fsc,
            $gst,
            $metro_status,
            $billing_zone,
            $invoice_series,
            $invoice_number,
            $manual_series,
            $manual_invoice_number,
            $surface_docket,
            $aircargo_docket,
            $premium_docket,
            $ptp_docket,
            $cod_docket,
            $international_docket,
            $ecom_express_docket,
            $ecom_surface_docket,
            $cnote_validation_enabled,
            $username
        );
    } else {
        // Insert new settings
        $stmt = $conn->prepare("INSERT INTO settings (
            username,
            express_cn_cost,
            aircargo_cn_cost,
            surface_cn_cost,
            premium_cn_cost,
            region_series,
            fsc,
            gst,
            metro_status,
            billing_zone,
            invoice_series,
            invoice_number,
            manual_series,
            manual_invoice_number,
            surface_docket,
            aircargo_docket,
            premium_docket,
            ptp_docket,
            cod_docket,
            international_docket,
            ecom_express_docket,
            ecom_surface_docket,
            cnote_validation_enabled,
            created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())");
        
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $conn->error);
        }
        
        $stmt->bind_param(
            "sdddsddsssssssssssssssi",
            $username,
            $express_cn_cost,
            $aircargo_cn_cost,
            $surface_cn_cost,
            $premium_cn_cost,
            $region_series,
            $fsc,
            $gst,
            $metro_status,
            $billing_zone,
            $invoice_series,
            $invoice_number,
            $manual_series,
            $manual_invoice_number,
            $surface_docket,
            $aircargo_docket,
            $premium_docket,
            $ptp_docket,
            $cod_docket,
            $international_docket,
            $ecom_express_docket,
            $ecom_surface_docket,
            $cnote_validation_enabled
        );
    }

    if (!$stmt->execute()) {
        throw new Exception("Execute failed: " . $stmt->error);
    }
    
    $_SESSION['settings_message'] = "Settings updated successfully!";
    
} catch (Exception $e) {
    error_log("Settings update error: " . $e->getMessage());
    $_SESSION['settings_message'] = "Error updating settings: " . $e->getMessage();
}

// Redirect back to settings page
header("Location: index.php?page=settings");
exit(); 