<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
include 'db_connect.php';

if (!isset($_SESSION['username'])) {
    die("Unauthorized");
}

// Fetch user settings
$username = $_SESSION['username'];
$settings_stmt = $conn->prepare("SELECT fsc, gst, express_cn_cost, aircargo_cn_cost, surface_cn_cost, premium_cn_cost FROM settings WHERE username = ?");
$settings_stmt->bind_param("s", $username);
$settings_stmt->execute();
$settings_result = $settings_stmt->get_result();
$settings = $settings_result->fetch_assoc();

// Set default values if settings don't exist
$fsc = $settings['fsc'] ?? 0;
$gst = $settings['gst'] ?? 0;
$cn_costs = [
    'Express' => $settings['express_cn_cost'] ?? 0,
    'Air Cargo' => $settings['aircargo_cn_cost'] ?? 0,
    'Surface' => $settings['surface_cn_cost'] ?? 0,
    'Premium' => $settings['premium_cn_cost'] ?? 0
];

// Return only the tables HTML
?>
<div style="display: flex; gap: 2rem; flex-wrap: wrap;">
    <div style="flex: 1; min-width: 600px;">
        <table style="width: 100%; border-collapse: separate; border-spacing: 0; background: white; border-radius: 12px; box-shadow: 0 2px 12px rgba(0,0,0,0.08); overflow: hidden; table-layout: auto;">
            <colgroup>
                <col style="width: auto; border-right: 1px solid var(--border-color);">
                <col style="width: 12%; border-right: 1px solid var(--border-color);">
                <col style="width: 12%; border-right: 1px solid var(--border-color);">
                <col style="width: 12%; border-right: 1px solid var(--border-color);">
                <col style="width: 15%; border-right: 1px solid var(--border-color);">
                <col style="width: 12%;">
            </colgroup>
            <thead>
                <tr>
                    <th style="background: var(--primary-color); color: white; padding: 1rem; text-align: left; font-weight: 500;">Product</th>
                    <th style="background: var(--primary-color); color: white; padding: 1rem; text-align: right; font-weight: 500;">Rate</th>
                    <th style="background: var(--primary-color); color: white; padding: 1rem; text-align: right; font-weight: 500;">FSC</th>
                    <th style="background: var(--primary-color); color: white; padding: 1rem; text-align: right; font-weight: 500;">GST</th>
                    <th style="background: var(--primary-color); color: white; padding: 1rem; text-align: right; font-weight: 500;">CN Charges</th>
                    <th style="background: var(--primary-color); color: white; padding: 1rem; text-align: right; font-weight: 500;">Total</th>
                </tr>
            </thead>
            <tbody>
                <?php if (isset($_SESSION['calculation_results']) && !empty($_SESSION['calculation_results'])): ?>
                    <?php foreach ($_SESSION['calculation_results'] as $product => $values): ?>
                    <tr>
                        <td style="padding: 1rem; border-bottom: 1px solid var(--border-color);"><?php echo htmlspecialchars($product); ?></td>
                        <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;"><?php echo number_format($values['base_rate'], 2); ?></td>
                        <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;"><?php echo number_format($values['base_rate'] * $fsc / 100, 2); ?></td>
                        <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;"><?php echo number_format(($values['base_rate'] + $values['base_rate'] * $fsc / 100) * $gst / 100, 2); ?></td>
                        <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;"><?php echo $values['base_rate'] > 0 ? number_format($cn_costs[$product], 2) : '0.00'; ?></td>
                        <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right; font-weight: bold;"><?php 
                            $base_rate = $values['base_rate'];
                            $fsc_amount = $base_rate * $fsc / 100;
                            $subtotal = $base_rate + $fsc_amount;
                            $gst_amount = $subtotal * $gst / 100;
                            $cn_charge = $values['base_rate'] > 0 ? $cn_costs[$product] : 0;
                            $total = $subtotal + $gst_amount + $cn_charge;
                            echo number_format($total, 2); 
                        ?></td>
                    </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <?php foreach (['Express', 'Air Cargo', 'Surface', 'Premium'] as $product): ?>
                    <tr>
                        <td style="padding: 1rem; border-bottom: 1px solid var(--border-color);"><?php echo htmlspecialchars($product); ?></td>
                        <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;">0.00</td>
                        <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;">0.00</td>
                        <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;">0.00</td>
                        <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;">0.00</td>
                        <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;">0.00</td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <div style="flex: 1; min-width: 600px;">
        <table style="width: 100%; border-collapse: separate; border-spacing: 0; background: white; border-radius: 12px; box-shadow: 0 2px 12px rgba(0,0,0,0.08); overflow: hidden; table-layout: auto;">
            <colgroup>
                <col style="width: auto; border-right: 1px solid var(--border-color);">
                <col style="width: 12%; border-right: 1px solid var(--border-color);">
                <col style="width: 12%; border-right: 1px solid var(--border-color);">
                <col style="width: 12%; border-right: 1px solid var(--border-color);">
                <col style="width: 15%; border-right: 1px solid var(--border-color);">
                <col style="width: 12%;">
            </colgroup>
            <thead>
                <tr>
                    <th style="background: #4CAF50; color: white; padding: 1rem; text-align: left; font-weight: 500;">Product</th>
                    <th style="background: #4CAF50; color: white; padding: 1rem; text-align: right; font-weight: 500;">Rate</th>
                    <th style="background: #4CAF50; color: white; padding: 1rem; text-align: right; font-weight: 500;">FSC</th>
                    <th style="background: #4CAF50; color: white; padding: 1rem; text-align: right; font-weight: 500;">GST</th>
                    <th style="background: #4CAF50; color: white; padding: 1rem; text-align: right; font-weight: 500;">CN Charges</th>
                    <th style="background: #4CAF50; color: white; padding: 1rem; text-align: right; font-weight: 500;">Total</th>
                </tr>
            </thead>
            <tbody>
                <?php if (isset($_SESSION['calculation_results']) && !empty($_SESSION['calculation_results'])): ?>
                    <?php foreach ($_SESSION['calculation_results'] as $product => $values): ?>
                    <?php
                        $margin_rate = $values['rate'];
                        $margin_fsc = $margin_rate * $fsc / 100;
                        $margin_subtotal = $margin_rate + $margin_fsc;
                        $margin_gst = $margin_subtotal * $gst / 100;
                        $margin_cn = $values['rate'] > 0 ? $cn_costs[$product] : 0;
                        $margin_total = $margin_subtotal + $margin_gst + $margin_cn;
                    ?>
                    <tr>
                        <td style="padding: 1rem; border-bottom: 1px solid var(--border-color);"><?php echo htmlspecialchars($product); ?></td>
                        <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;"><?php echo number_format($margin_rate, 2); ?></td>
                        <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;"><?php echo number_format($margin_fsc, 2); ?></td>
                        <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;"><?php echo number_format($margin_gst, 2); ?></td>
                        <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;"><?php echo $values['rate'] > 0 ? number_format($margin_cn, 2) : '0.00'; ?></td>
                        <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right; font-weight: bold;"><?php echo number_format($margin_total, 2); ?></td>
                    </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <?php foreach (['Express', 'Air Cargo', 'Surface', 'Premium'] as $product): ?>
                    <tr>
                        <td style="padding: 1rem; border-bottom: 1px solid var(--border-color);"><?php echo htmlspecialchars($product); ?></td>
                        <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;">0.00</td>
                        <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;">0.00</td>
                        <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;">0.00</td>
                        <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;">0.00</td>
                        <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;">0.00</td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div> 