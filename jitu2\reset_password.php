<?php
require_once 'config.php';

// Check if token exists
if (!isset($_GET['token'])) {
    header('Location: index.html');
    exit;
}

$token = $_GET['token'];
error_log("Reset password token received: " . $token);

// Verify token and get email
try {
    // First, let's check if the token exists in the database
    $stmt = $pdo->prepare("SELECT * FROM password_resets WHERE token = ?");
    $stmt->execute([$token]);
    $tokenCheck = $stmt->fetch();
    
    if (!$tokenCheck) {
        error_log("Token not found in database: " . $token);
        $_SESSION['error'] = 'Invalid password reset link. Please request a new one.';
        header('Location: index.html');
        exit;
    }

    // Now check if it's valid and not expired
    $stmt = $pdo->prepare("SELECT email FROM password_resets WHERE token = ? AND expires_at > NOW() AND used = 0");
    $stmt->execute([$token]);
    $reset = $stmt->fetch();

    if (!$reset) {
        error_log("Token validation failed - Token: " . $token . 
                  ", Expires at: " . $tokenCheck['expires_at'] . 
                  ", Used: " . $tokenCheck['used']);
        $_SESSION['error'] = 'Invalid or expired password reset link. Please request a new one.';
        header('Location: index.html');
        exit;
    }

    $email = $reset['email'];
    error_log("Valid token found for email: " . $email);
} catch (PDOException $e) {
    error_log("Password reset verification error: " . $e->getMessage());
    $_SESSION['error'] = 'An error occurred. Please try again.';
    header('Location: index.html');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - Astra Logistics Manager</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        :root {
            --primary-color: #4f46e5;
            --secondary-color: #7c3aed;
            --accent-color: #06b6d4;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --background-color: #ffffff;
            --text-color: #1f2937;
            --text-muted: #64748b;
            --border-color: rgba(0, 0, 0, 0.1);
            --hover-bg: rgba(79, 70, 229, 0.08);
            --glass-bg: rgba(255, 255, 255, 0.98);
            --glass-border: rgba(255, 255, 255, 0.2);
            --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', system-ui, -apple-system, sans-serif;
            line-height: 1.5;
            color: var(--text-color);
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .reset-container {
            background: var(--glass-bg);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            padding: 2rem;
            width: 100%;
            max-width: 400px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .reset-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .reset-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: 0.5rem;
        }

        .reset-subtitle {
            font-size: 0.9rem;
            color: var(--text-muted);
        }

        .alert {
            padding: 0.75rem 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            display: none;
        }

        .alert-error {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .reset-form {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-group label {
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-color);
        }

        .form-group input {
            padding: 0.75rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.5);
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .submit-btn {
            background: var(--gradient-primary);
            color: white;
            border: none;
            padding: 0.75rem;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.2);
        }

        .submit-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }

        .back-to-login {
            text-align: center;
            margin-top: 1rem;
            font-size: 0.9rem;
            color: var(--text-muted);
        }

        .back-to-login a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }

        .back-to-login a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="reset-header">
            <h1 class="reset-title">Reset Your Password</h1>
            <p class="reset-subtitle">Enter your new password below</p>
        </div>
        <div class="alert alert-error" id="resetAlert"></div>
        <form class="reset-form" id="resetForm" onsubmit="handleReset(event)">
            <input type="hidden" id="token" value="<?php echo htmlspecialchars($token); ?>">
            <div class="form-group">
                <label for="password">New Password</label>
                <input type="password" id="password" name="password" required minlength="8">
            </div>
            <div class="form-group">
                <label for="confirmPassword">Confirm New Password</label>
                <input type="password" id="confirmPassword" name="confirmPassword" required minlength="8">
            </div>
            <button type="submit" class="submit-btn">
                <i class="fas fa-key"></i> Reset Password
            </button>
        </form>
        <div class="back-to-login">
            <p>Remember your password? <a href="index.html">Back to Login</a></p>
        </div>
    </div>

    <script>
        function handleReset(event) {
            event.preventDefault();
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const token = document.getElementById('token').value;
            const alertEl = document.getElementById('resetAlert');

            console.log('Token being sent:', token); // Debug log

            if (password !== confirmPassword) {
                alertEl.className = 'alert alert-error';
                alertEl.textContent = 'Passwords do not match.';
                alertEl.style.display = 'block';
                return;
            }

            // Show loading state
            const submitBtn = event.target.querySelector('.submit-btn');
            const originalBtnText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Resetting...';
            submitBtn.disabled = true;

            // Send reset request
            fetch('reset_password_process.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    token: token,
                    password: password
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alertEl.className = 'alert alert-success';
                    alertEl.textContent = data.message;
                    alertEl.style.display = 'block';
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 2000);
                } else {
                    alertEl.className = 'alert alert-error';
                    alertEl.textContent = data.message;
                    alertEl.style.display = 'block';
                    submitBtn.innerHTML = originalBtnText;
                    submitBtn.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error); // Debug log
                alertEl.className = 'alert alert-error';
                alertEl.textContent = 'An error occurred. Please try again.';
                alertEl.style.display = 'block';
                submitBtn.innerHTML = originalBtnText;
                submitBtn.disabled = false;
            });
        }
    </script>
</body>
</html> 