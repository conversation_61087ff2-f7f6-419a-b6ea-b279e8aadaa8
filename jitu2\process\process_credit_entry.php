<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../logs/process_credit_entry.log');

session_start();
include '../db_connect.php'; // Database connection

// Add debugging output to browser console via JavaScript
function console_log($message, $data = null) {
    $json_data = $data ? json_encode($data) : 'null';
    echo "<script>console.log('🐛 CREDIT PHP DEBUG: " . addslashes($message) . "', " . $json_data . ");</script>";
}

error_log("=== Starting process_credit_entry.php execution ===");
error_log("Session username: " . (isset($_SESSION['username']) ? $_SESSION['username'] : 'Not set'));

if (!isset($_SESSION['username'])) {
    error_log("Error: User not logged in");
    die("<script>alert('Error: You must be logged in.'); window.location.href='login.php';</script>");
}

$username = $_SESSION['username']; // Get the logged-in user

// Debug: Log initial processing start
echo "<script>
console.log('🚀 PROCESS_CREDIT_ENTRY.PHP START');
console.log('👤 Session Username:', '" . addslashes($username) . "');
console.log('📥 POST Data Received:', " . json_encode($_POST) . ");
console.log('🔍 Confirm Duplicate Flag:', '" . (isset($_POST['confirm_duplicate']) ? $_POST['confirm_duplicate'] : 'NOT SET') . "');
</script>";

// ========================= SINGLE CREDIT ENTRY ========================= //
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['customer'])) {
    error_log("Processing single credit entry");
    error_log("POST data received: " . print_r($_POST, true));

    $customer = $_POST['customer'];
    $to_party = $_POST['to_party'] ?? ''; // Make to_party optional for credit entries
    $docket_no = $_POST['docket_no'];
    $docket_date = $_POST['docket_date'];
    $pincode = $_POST['pincode'];
    $destination = $_POST['destination'];
    $original_input_weight = floatval($_POST['weight']); // Save immediately after reading from form
    $weight = $original_input_weight; // Use $weight for calculations below
    $mode_of_tsp = $_POST['mode_of_tsp'];
    $waybill_value = $_POST['waybill_value'];
    $remarks = $_POST['remarks'];

    // Validate and standardize date format
    try {
        $date = new DateTime($docket_date);
        $docket_date = $date->format('Y-m-d');
    } catch (Exception $e) {
        die("<script>alert('❌ Error: Invalid date format. Please use YYYY-MM-DD format.'); window.history.back();</script>");
    }

    error_log("Form data extracted:");
    error_log("Customer: " . $customer);
    error_log("Docket No: " . $docket_no);
    error_log("Weight: " . $weight);
    error_log("Mode of TSP: " . $mode_of_tsp);

    // Fetch customer percentage rates
    error_log("Fetching customer rates for: " . $customer);
    $customer_rates_stmt = $conn->prepare("SELECT waybill_percent, owner_risk, carrier_risk FROM customers WHERE short_name = ? AND username = ?");
    if (!$customer_rates_stmt) {
        error_log("Error preparing customer rates statement: " . $conn->error);
    }

    $customer_rates_stmt->bind_param("ss", $customer, $username);
    $customer_rates_stmt->execute();
    $customer_rates_result = $customer_rates_stmt->get_result();
    $customer_rates = $customer_rates_result->fetch_assoc();
    $customer_rates_stmt->close();

    error_log("Customer rates fetched: " . print_r($customer_rates, true));

    // Calculate percentage values
    $waybill_value = floatval($waybill_value);
    $waybill_percent = floatval($customer_rates['waybill_percent']);
    $owner_risk = floatval($customer_rates['owner_risk']);
    $carrier_risk = floatval($customer_rates['carrier_risk']);

    $waybill_percent_value = ($waybill_value * $waybill_percent) / 100;
    $owner_risk_value = ($waybill_value * $owner_risk) / 100;
    $carrier_risk_value = ($waybill_value * $carrier_risk) / 100;

    error_log("Calculated values:");
    error_log("Waybill percent value: " . $waybill_percent_value);
    error_log("Owner risk value: " . $owner_risk_value);
    error_log("Carrier risk value: " . $carrier_risk_value);

    // Check if C-Note validation is enabled, then validate docket number
    $settings_stmt = $conn->prepare("SELECT cnote_validation_enabled FROM settings WHERE username = ?");
    $settings_stmt->bind_param("s", $username);
    $settings_stmt->execute();
    $settings_result = $settings_stmt->get_result();
    $settings_data = $settings_result->fetch_assoc();
    $cnote_validation_enabled = $settings_data['cnote_validation_enabled'] ?? 1; // Default to enabled
    $settings_stmt->close();

    if ($cnote_validation_enabled) {
        // Validate docket number exists in cn_entries and matches with selected customer
        error_log("Starting docket validation in process_credit_entry.php");
        error_log("Validating - Docket: $docket_no, Customer: $customer");

        $query = "SELECT cn_number, customer FROM cn_entries WHERE cn_number = ?";
        error_log("Executing query: $query with docket_no: $docket_no");

        $validate_stmt = $conn->prepare($query);
        if (!$validate_stmt) {
            error_log("Error preparing validation statement: " . $conn->error);
        }

        $validate_stmt->bind_param("s", $docket_no);
        $validate_stmt->execute();
        $validate_result = $validate_stmt->get_result();

        if ($validate_result->num_rows === 0) {
            error_log("CN number not found in database in process_credit_entry.php");
            die("<script>alert('Error: CN Number not found in database!'); window.history.back();</script>");
        }

        $cn_data = $validate_result->fetch_assoc();
        error_log("Found CN data - Raw data: " . print_r($cn_data, true));
        error_log("Found CN data - Stored Customer: {$cn_data['customer']}, Input Customer: $customer");

        // If CN exists but has no customer allocated, allow entry
        if (empty($cn_data['customer']) || $cn_data['customer'] === null) {
            error_log("CN exists but not allocated to any customer - allowing entry");
        } else {
            // If CN is allocated to a customer, check if it matches
            if ($cn_data['customer'] === $customer) {
                error_log("Customer match confirmed");
            } else {
                error_log("Customer mismatch detected in process_credit_entry.php");
                die("<script>alert('Error: CN Number is already allocated to another customer!'); window.history.back();</script>");
            }
        }
        $validate_stmt->close();
    } else {
        error_log("C-Note validation is disabled - skipping validation");
    }

    // Check for Duplicate Docket with Confirmation
    error_log("Checking for duplicate docket: " . $docket_no);
    $check_stmt = $conn->prepare("SELECT docket_no, docket_date, created_at, entry_type FROM transactions WHERE docket_no = ? AND username = ? ORDER BY created_at DESC LIMIT 1");
    if (!$check_stmt) {
        error_log("Error preparing duplicate check statement: " . $conn->error);
    }

    $check_stmt->bind_param("ss", $docket_no, $username);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    $existing_entry = $check_result->fetch_assoc();
    $check_stmt->close();

    // Check if user has confirmed duplicate entry
    $confirm_duplicate = isset($_POST['confirm_duplicate']) && $_POST['confirm_duplicate'] == '1';

    // Format the existing entry date information
    $existing_date_info = '';
    $existing_entry_type = '';
    if ($existing_entry) {
        $existing_entry_type = ucfirst($existing_entry['entry_type']);
        $docket_date_existing = date('d-M-Y', strtotime($existing_entry['docket_date']));
        $created_date = date('d-M-Y H:i', strtotime($existing_entry['created_at']));
        $existing_date_info = "Originally used on: $docket_date_existing (Entry created: $created_date)";
    }

    echo "<script>
    console.log('🔍 CREDIT DUPLICATE CHECK RESULTS:');
    console.log('  Duplicate entry found:', " . ($existing_entry ? 'true' : 'false') . ");
    console.log('  Existing entry type:', '" . addslashes($existing_entry_type) . "');
    console.log('  Existing docket date:', '" . addslashes($existing_entry ? $existing_entry['docket_date'] : 'N/A') . "');
    console.log('  Existing created date:', '" . addslashes($existing_entry ? $existing_entry['created_at'] : 'N/A') . "');
    console.log('  Confirm duplicate flag:', " . ($confirm_duplicate ? 'true' : 'false') . ");
    console.log('  Will show confirmation:', " . (($existing_entry && !$confirm_duplicate) ? 'true' : 'false') . ");
    </script>";

    if ($existing_entry && !$confirm_duplicate) {
        // Duplicate exists and user hasn't confirmed - ask for confirmation
        echo "<script>console.log('⚠️ SHOWING CREDIT DUPLICATE CONFIRMATION DIALOG');</script>";
        error_log("Duplicate docket found: " . $docket_no . " - showing confirmation dialog");
        $conn->close();

        // Create form data to resubmit with confirmation
        $form_data = '';
        foreach ($_POST as $key => $value) {
            if ($key !== 'confirm_duplicate') {
                $form_data .= '<input type="hidden" name="' . htmlspecialchars($key) . '" value="' . htmlspecialchars($value) . '">';
            }
        }

        echo '
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Credit Entry - Duplicate Confirmation</title>
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }

                body {
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
                    background-color: rgba(0, 0, 0, 0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    min-height: 100vh;
                    padding: 20px;
                }

                .modal {
                    background: white;
                    border-radius: 12px;
                    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                    max-width: 500px;
                    width: 100%;
                    animation: modalSlideIn 0.3s ease-out;
                    overflow: hidden;
                }

                @keyframes modalSlideIn {
                    from {
                        opacity: 0;
                        transform: translateY(-50px) scale(0.9);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0) scale(1);
                    }
                }

                .modal-header {
                    background: linear-gradient(135deg, #6c5ce7, #a29bfe);
                    color: white;
                    padding: 24px;
                    text-align: center;
                }

                .modal-icon {
                    font-size: 48px;
                    margin-bottom: 12px;
                    display: block;
                }

                .modal-title {
                    font-size: 24px;
                    font-weight: 600;
                    margin-bottom: 8px;
                }

                .modal-subtitle {
                    font-size: 16px;
                    opacity: 0.9;
                }

                .modal-body {
                    padding: 32px 24px;
                    text-align: center;
                }

                .warning-message {
                    font-size: 18px;
                    color: #2c3e50;
                    line-height: 1.6;
                    margin-bottom: 8px;
                }

                .docket-highlight {
                    font-weight: 700;
                    color: #6c5ce7;
                    background: #e8e4ff;
                    padding: 4px 8px;
                    border-radius: 6px;
                    display: inline-block;
                    margin: 0 4px;
                }

                .sub-message {
                    font-size: 16px;
                    color: #7f8c8d;
                    margin-top: 16px;
                }

                .credit-badge {
                    display: inline-block;
                    background: linear-gradient(135deg, #6c5ce7, #a29bfe);
                    color: white;
                    padding: 4px 12px;
                    border-radius: 20px;
                    font-size: 12px;
                    font-weight: 600;
                    margin-bottom: 16px;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }

                .existing-entry-info {
                    background: #f8f9fa;
                    border: 1px solid #e9ecef;
                    border-radius: 8px;
                    padding: 16px;
                    margin: 20px 0;
                    text-align: left;
                }

                .entry-type-badge {
                    display: inline-block;
                    background: linear-gradient(135deg, #6c5ce7, #a29bfe);
                    color: white;
                    padding: 4px 10px;
                    border-radius: 12px;
                    font-size: 11px;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                    margin-bottom: 12px;
                }

                .date-info {
                    font-size: 14px;
                    color: #495057;
                    line-height: 1.6;
                    margin: 0;
                }

                .date-info strong {
                    color: #343a40;
                    font-weight: 600;
                }

                .modal-footer {
                    padding: 24px;
                    background: #f8f9fa;
                    display: flex;
                    gap: 12px;
                    justify-content: center;
                }

                .btn {
                    padding: 12px 32px;
                    border: none;
                    border-radius: 8px;
                    font-size: 16px;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    min-width: 120px;
                }

                .btn-confirm {
                    background: linear-gradient(135deg, #6c5ce7, #a29bfe);
                    color: white;
                }

                .btn-confirm:hover {
                    background: linear-gradient(135deg, #5f4fcf, #9085e8);
                    transform: translateY(-2px);
                    box-shadow: 0 8px 25px rgba(108, 92, 231, 0.3);
                }

                .btn-cancel {
                    background: #ddd;
                    color: #666;
                }

                .btn-cancel:hover {
                    background: #ccc;
                    transform: translateY(-2px);
                    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
                }

                .loading {
                    display: none;
                    text-align: center;
                    padding: 20px;
                }

                .spinner {
                    border: 3px solid #f3f3f3;
                    border-top: 3px solid #6c5ce7;
                    border-radius: 50%;
                    width: 30px;
                    height: 30px;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 12px;
                }

                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            </style>
        </head>
        <body>
            <div class="modal">
                <div class="modal-header">
                    <span class="modal-icon">💳</span>
                    <h2 class="modal-title">Credit Entry Duplicate</h2>
                    <p class="modal-subtitle">This docket number already exists</p>
                </div>

                <div class="modal-body">
                    <div class="credit-badge">Credit Entry</div>
                    <p class="warning-message">
                        Docket No. <span class="docket-highlight">' . htmlspecialchars($docket_no) . '</span> already exists for this user.
                    </p>
                    <div class="existing-entry-info">
                        <div class="entry-type-badge">' . htmlspecialchars($existing_entry_type) . ' Entry</div>
                        <p class="date-info">
                            <strong>📅 Docket Date:</strong> ' . htmlspecialchars($docket_date_existing) . '<br>
                            <strong>🕒 Entry Created:</strong> ' . htmlspecialchars($created_date) . '
                        </p>
                    </div>
                    <p class="sub-message">
                        Do you want to add this duplicate credit entry anyway?
                    </p>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-cancel" onclick="cancelDuplicate()">
                        ❌ Cancel
                    </button>
                    <button type="button" class="btn btn-confirm" onclick="confirmDuplicate()">
                        ✅ Yes, Add Duplicate
                    </button>
                </div>

                <div class="loading" id="loadingDiv">
                    <div class="spinner"></div>
                    <p>Processing credit entry...</p>
                </div>
            </div>

            <form id="confirmForm" method="POST" action="process_credit_entry.php" style="display:none;">
                ' . $form_data . '
                <input type="hidden" name="confirm_duplicate" value="1">
            </form>

            <script>
                // Detailed console debugging
                console.log("🔍 CREDIT MODAL DUPLICATE CONFIRMATION DEBUG START");
                console.log("📋 Docket Number:", "' . htmlspecialchars($docket_no) . '");
                console.log("👤 Username:", "' . htmlspecialchars($username) . '");
                console.log("📝 Form Data Length:", ' . strlen($form_data) . ');
                console.log("🔗 Action URL:", "process_credit_entry.php");

                function confirmDuplicate() {
                    console.log("✅ User confirmed duplicate credit entry");

                    const confirmForm = document.getElementById("confirmForm");
                    const loadingDiv = document.getElementById("loadingDiv");
                    const modalFooter = document.querySelector(".modal-footer");
                    const modalBody = document.querySelector(".modal-body");

                    if (!confirmForm) {
                        console.error("❌ ERROR: confirmForm element not found!");
                        alert("Error: Form not found. Please try again.");
                        window.history.back();
                        return;
                    }

                    // Show loading state
                    modalFooter.style.display = "none";
                    modalBody.style.display = "none";
                    loadingDiv.style.display = "block";

                    console.log("📤 Submitting credit form with duplicate confirmation");
                    console.log("🔢 Form elements count:", confirmForm.elements.length);

                    // Log all form elements being submitted
                    for (let i = 0; i < confirmForm.elements.length; i++) {
                        const element = confirmForm.elements[i];
                        console.log("  Field:", element.name, "=", element.value);
                    }

                    try {
                        confirmForm.submit();
                        console.log("📨 Credit form submitted successfully");
                    } catch (error) {
                        console.error("❌ Form submission error:", error);
                        alert("Error submitting form: " + error.message);
                        // Restore modal state on error
                        modalFooter.style.display = "flex";
                        modalBody.style.display = "block";
                        loadingDiv.style.display = "none";
                    }
                }

                function cancelDuplicate() {
                    console.log("❌ User cancelled duplicate credit entry");
                    window.history.back();
                }

                // Additional error handling
                window.addEventListener("error", function(e) {
                    console.error("🚨 JavaScript Error:", e.error);
                    console.error("📍 Error location:", e.filename + ":" + e.lineno);
                });

                // Auto-focus on confirm button for better UX
                document.addEventListener("DOMContentLoaded", function() {
                    console.log("🚀 Credit modal loaded and ready");
                    document.querySelector(".btn-confirm").focus();
                });

                // Allow Enter key to confirm, Escape to cancel
                document.addEventListener("keydown", function(e) {
                    if (e.key === "Enter") {
                        confirmDuplicate();
                    } else if (e.key === "Escape") {
                        cancelDuplicate();
                    }
                });

                console.log("🔍 CREDIT MODAL DUPLICATE CONFIRMATION DEBUG END");
            </script>
        </body>
        </html>
        ';
        exit;
    }

    // Fetch Zone from Pincode
    error_log("Fetching zone for pincode: " . $pincode);
    $zone = '';
    $ts_zone = '';
    $pr_zone = '';
    $zone_query = "SELECT zone, ts_zone, pr_zone FROM pincode_data WHERE pincode=?";
    $zone_stmt = $conn->prepare($zone_query);
    if (!$zone_stmt) {
        error_log("Error preparing zone statement: " . $conn->error);
    }

    $zone_stmt->bind_param("s", $pincode);
    $zone_stmt->execute();
    $zone_result = $zone_stmt->get_result();
    if ($zone_row = $zone_result->fetch_assoc()) {
        $zone = $zone_row['zone'];
        $ts_zone = $zone_row['ts_zone'];
        $pr_zone = $zone_row['pr_zone'];
        error_log("Zone found: " . $zone . ", TS Zone: " . $ts_zone . ", PR Zone: " . $pr_zone);
    } else {
        error_log("No zone found for pincode: " . $pincode);
        die("<script>alert('Error: Zone not found for pincode!'); window.history.back();</script>");
    }
    $zone_stmt->close();

    // Calculate old rate for amount
    error_log("Calculating old rate for amount");
    $amount = 0;

    // Fetch rates from rate_master for amount calculation
    $rate_mode = $mode_of_tsp === 'Air Cargo' ? 'Express' : $mode_of_tsp;
    $rate_query = "";
    if ($mode_of_tsp === 'Premium') {
        $rate_query = "SELECT * FROM rate_master WHERE short_name=? AND mode_of_tsp=? AND UPPER(pr_zone)=UPPER(?)";
        error_log("Using Premium mode with pr_zone: " . $pr_zone);
    } else {
        $rate_query = "SELECT * FROM rate_master WHERE short_name=? AND mode_of_tsp=? AND UPPER(zone)=UPPER(?)";
        error_log("Using regular mode with zone: " . $zone);
    }

    $rate_stmt = $conn->prepare($rate_query);
    if (!$rate_stmt) {
        error_log("Error preparing rate statement: " . $conn->error);
        die("<script>alert('Error: Database error occurred!'); window.history.back();</script>");
    }

    // Bind parameters based on mode
    if ($mode_of_tsp === 'Premium') {
        $rate_stmt->bind_param("sss", $customer, $rate_mode, $pr_zone);
    } else {
        $rate_stmt->bind_param("sss", $customer, $rate_mode, $zone);
    }

    $rate_stmt->execute();
    $rate_result = $rate_stmt->get_result();

    if ($rate_row = $rate_result->fetch_assoc()) {
        error_log("Rate data found for amount calculation: " . print_r($rate_row, true));
        if ($rate_mode === 'Surface') {
            // For Surface mode
            $min_weight = floatval($rate_row['above_3kg']); // minimum weight
            $actual_weight = max($weight, $min_weight);
            $rounded_weight = ceil($actual_weight);

            // Select rate based on weight slabs for Surface mode
            if ($rounded_weight <= 10) {
                $rate_used = floatval($rate_row['up_to_0250']); // 1-10 kg rate
                error_log("Surface mode - Using up_to_0250 rate: " . $rate_used);
            } elseif ($rounded_weight <= 20) {
                $rate_used = floatval($rate_row['up_to_0500']); // 11-20 kg rate
                error_log("Surface mode - Using up_to_0500 rate: " . $rate_used);
            } else {
                $rate_used = floatval($rate_row['addl_500gm']); // Above 20 kg rate
                error_log("Surface mode - Using addl_500gm rate: " . $rate_used);
            }

            $amount = $rounded_weight * $rate_used;
            error_log("Surface mode - Final amount: " . $amount);
        } else if ($rate_mode === 'Premium') {
            // For Premium mode
            if ($weight <= 0.250) {
                // First 250 grams, no rounding
                $amount = floatval($rate_row['up_to_0250']);
                error_log("Premium mode - Using up_to_0250 rate: " . $amount);
            } else {
                // Round weight to nearest 500gms up to 10kg
                if ($weight <= 10) {
                    $weight = ceil($weight * 2) / 2; // Round to nearest 500gms
                    if ($weight <= 0.500) {
                        $amount = floatval($rate_row['up_to_0500']);
                        error_log("Premium mode - Using up_to_0500 rate: " . $amount);
                    } else {
                        $amount = floatval($rate_row['up_to_0500']) + (($weight - 0.500) / 0.500) * floatval($rate_row['addl_500gm']);
                        error_log("Premium mode - Calculated amount with addl_500gm: " . $amount);
                    }
                } else {
                    // Above 10kg - apply flat per kg rate
                    $amount = ceil($weight) * floatval($rate_row['above_3kg']);
                    error_log("Premium mode - Using above_3kg rate: " . $amount);
                }
            }
        } else {
            // For Express mode
            if ($weight <= 0.250) {
                // First 250 grams, no rounding
                $amount = floatval($rate_row['up_to_0250']);
                error_log("Express mode - Using up_to_0250 rate: " . $amount);
            } else {
                // Round weight only if above 250 grams
                $weight = ceil($weight * 2) / 2;

                if ($weight <= 0.500) {
                    $amount = floatval($rate_row['up_to_0500']);
                    error_log("Express mode - Using up_to_0500 rate: " . $amount);
                } elseif ($weight <= 3) {
                    $amount = floatval($rate_row['up_to_0500']) + (($weight - 0.500) / 0.500) * floatval($rate_row['addl_500gm']);
                    error_log("Express mode - Calculated amount with addl_500gm: " . $amount);
                } else {
                    // Above 3 kg calculation
                    $amount = $weight * floatval($rate_row['above_3kg']);
                    error_log("Express mode - Using above_3kg rate: " . $amount);
                }
            }
        }
    } else {
        error_log("No rate found for amount calculation - mode: " . $rate_mode . ", zone: " . $zone);
        die("<script>alert('Error: Rate not found for given parameters! Please check if rates are configured for " . $rate_mode . " mode in the selected zone (" . $zone . ") for customer " . $customer . ".'); window.history.back();</script>");
    }

    // Calculate entry_ts for all modes including Premium
    error_log("Calculating new rate for entry_ts");
    $entry_ts = 0;

    // Fetch rates from ts_rate_master
    $ts_rate_query = "SELECT * FROM ts_rate_master WHERE username=? AND mode_of_tsp=? AND UPPER(zone)=UPPER(?)";
    $ts_rate_stmt = $conn->prepare($ts_rate_query);
    if (!$ts_rate_stmt) {
        error_log("Error preparing ts_rate statement: " . $conn->error);
        die("<script>alert('Error: Database error occurred!'); window.history.back();</script>");
    }

    $ts_rate_stmt->bind_param("sss", $username, $mode_of_tsp, $ts_zone);
    error_log("Using TS Zone for " . $mode_of_tsp . " mode: " . $ts_zone);

    $ts_rate_stmt->execute();
    $ts_rate_result = $ts_rate_stmt->get_result();

    if ($ts_rate_row = $ts_rate_result->fetch_assoc()) {
        error_log("TS Rate data found: " . print_r($ts_rate_row, true));
        $weight = floatval($weight);

        // Calculate rate based on weight slabs
        if ($mode_of_tsp === 'Surface') {
            // Existing Surface mode calculation
            // Apply minimum weight check first
            $min_weight = floatval($ts_rate_row['min_weight']);
            if ($weight < $min_weight) {
                $weight = $min_weight;
                error_log("Applied minimum weight: " . $min_weight);
            }

            // Round weight to nearest 1kg for Surface mode
            $rounded_weight = ceil($weight);
            error_log("Surface mode - Rounded weight: " . $rounded_weight);

            // Calculate rate based on weight slabs for Surface mode
            if ($rounded_weight <= 5.000) {
                $entry_ts = $rounded_weight * floatval($ts_rate_row['up_to_5kg']);
                error_log("Surface mode - Using up_to_5kg rate: " . $entry_ts);
            } elseif ($rounded_weight <= 10.000) {
                $entry_ts = $rounded_weight * floatval($ts_rate_row['up_to_10kg']);
                error_log("Surface mode - Using up_to_10kg rate: " . $entry_ts);
            } elseif ($rounded_weight <= 25.000) {
                $entry_ts = $rounded_weight * floatval($ts_rate_row['up_to_25kg']);
                error_log("Surface mode - Using up_to_25kg rate: " . $entry_ts);
            } elseif ($rounded_weight <= 50.000) {
                $entry_ts = $rounded_weight * floatval($ts_rate_row['up_to_50kg']);
                error_log("Surface mode - Using up_to_50kg rate: " . $entry_ts);
            } else {
                $entry_ts = $rounded_weight * floatval($ts_rate_row['above_50kg']);
                error_log("Surface mode - Using above_50kg rate: " . $entry_ts);
            }
        } elseif ($mode_of_tsp === 'Air Cargo') {
            // Apply minimum weight check first
            $min_weight = floatval($ts_rate_row['min_weight']);
            if ($weight < $min_weight) {
                $weight = $min_weight;
                error_log("Applied minimum weight: " . $min_weight);
            }

            // Round weight to nearest 1kg for Air Cargo mode (same as Surface)
            $rounded_weight = ceil($weight);
            error_log("Air Cargo mode - Rounded weight: " . $rounded_weight);

            // Calculate rate based on weight slabs for Air Cargo mode (same as Surface)
            if ($rounded_weight <= 5.000) {
                $entry_ts = $rounded_weight * floatval($ts_rate_row['up_to_5kg']);
                error_log("Air Cargo mode - Using up_to_5kg rate: " . $entry_ts);
            } elseif ($rounded_weight <= 10.000) {
                $entry_ts = $rounded_weight * floatval($ts_rate_row['up_to_10kg']);
                error_log("Air Cargo mode - Using up_to_10kg rate: " . $entry_ts);
            } elseif ($rounded_weight <= 25.000) {
                $entry_ts = $rounded_weight * floatval($ts_rate_row['up_to_25kg']);
                error_log("Air Cargo mode - Using up_to_25kg rate: " . $entry_ts);
            } elseif ($rounded_weight <= 50.000) {
                $entry_ts = $rounded_weight * floatval($ts_rate_row['up_to_50kg']);
                error_log("Air Cargo mode - Using up_to_50kg rate: " . $entry_ts);
            } else {
                $entry_ts = $rounded_weight * floatval($ts_rate_row['above_50kg']);
                error_log("Air Cargo mode - Using above_50kg rate: " . $entry_ts);
            }
        } elseif ($mode_of_tsp === 'Premium') {
            // Apply minimum weight check first
            $min_weight = floatval($ts_rate_row['min_weight']);
            if ($weight < $min_weight) {
                $weight = $min_weight;
                error_log("Applied minimum weight: " . $min_weight);
            }

            // Calculate rate based on weight slabs for Premium mode
            if ($weight <= 0.250) {
                // Up to 250 grams - flat rate
                $entry_ts = floatval($ts_rate_row['up_to_0250']);
                error_log("Premium mode - Using up_to_0250 rate: " . $entry_ts);
            } elseif ($weight <= 0.500) {
                // Up to 500 grams - flat rate
                $entry_ts = floatval($ts_rate_row['up_to_0500']);
                error_log("Premium mode - Using up_to_0500 rate: " . $entry_ts);
            } elseif ($weight <= 10.000) {
                // Above 500 grams to 10kg - round to nearest 500gms and add additional rate
                $rounded_weight = ceil($weight * 2) / 2; // Round to nearest 500gms
                $base_rate = floatval($ts_rate_row['up_to_0500']);
                $additional_weight = ($rounded_weight - 0.500) * 2; // Convert to 500gm units
                $entry_ts = $base_rate + ($additional_weight * floatval($ts_rate_row['addl_500gm']));
                error_log("Premium mode - Calculated rate with addl_500gm: " . $entry_ts);
            } else {
                // Above 10kg - round to nearest 1kg and apply per kg rate
                $rounded_weight = ceil($weight);
                $entry_ts = $rounded_weight * floatval($ts_rate_row['above_50kg']);
                error_log("Premium mode - Using above_50kg rate: " . $entry_ts);
            }
        } else {
            // Express rate calculation
            if ($weight <= 0.100) {
                $entry_ts = floatval($ts_rate_row['up_to_0100']);
                error_log("Weight <= 0.100kg, rate: " . $entry_ts);
            } elseif ($weight <= 0.250) {
                $entry_ts = floatval($ts_rate_row['up_to_0250']);
                error_log("Weight <= 0.250kg, rate: " . $entry_ts);
            } elseif ($weight <= 0.500) {
                $entry_ts = floatval($ts_rate_row['up_to_0500']);
                error_log("Weight <= 0.500kg, rate: " . $entry_ts);
            } elseif ($weight <= 3.000) {
                $base_rate = floatval($ts_rate_row['up_to_0500']);
                $additional_weight = ceil(($weight - 0.500) * 2);
                $entry_ts = $base_rate + ($additional_weight * floatval($ts_rate_row['addl_500gm']));
                error_log("Weight <= 3.000kg, calculated rate: " . $entry_ts);
            } elseif ($weight <= 5.000) {
                $entry_ts = $weight * floatval($ts_rate_row['up_to_5kg']);
                error_log("Weight <= 5.000kg, rate: " . $entry_ts);
            } elseif ($weight <= 10.000) {
                $entry_ts = $weight * floatval($ts_rate_row['up_to_10kg']);
                error_log("Weight <= 10.000kg, rate: " . $entry_ts);
            } elseif ($weight <= 25.000) {
                $entry_ts = $weight * floatval($ts_rate_row['up_to_25kg']);
                error_log("Weight <= 25.000kg, rate: " . $entry_ts);
            } elseif ($weight <= 50.000) {
                $entry_ts = $weight * floatval($ts_rate_row['up_to_50kg']);
                error_log("Weight <= 50.000kg, rate: " . $entry_ts);
            } else {
                $entry_ts = $weight * floatval($ts_rate_row['above_50kg']);
                error_log("Weight > 50.000kg, rate: " . $entry_ts);
            }

            // Apply minimum weight check for Express mode
            $min_weight = floatval($ts_rate_row['min_weight']);
            if ($weight < $min_weight) {
                $weight = $min_weight;
                error_log("Applied minimum weight: " . $min_weight);
            }
        }
    } else {
        error_log("No TS rate found for mode: " . $mode_of_tsp . ", zone: " . $ts_zone);
        die("<script>alert('Error: Rate not found in ts_rate_master for given parameters!'); window.history.back();</script>");
    }
    $ts_rate_stmt->close();

    // Insert into Transactions Table
    error_log("Preparing to insert into transactions table");

    echo "<script>
    console.log('✅ PROCEEDING TO DATABASE INSERTION');
    console.log('📋 Final Data to Insert:');
    console.log('  docket_no:', '" . addslashes($docket_no) . "');
    console.log('  customer:', '" . addslashes($customer) . "');
    console.log('  docket_date:', '" . addslashes($docket_date) . "');
    console.log('  pincode:', '" . addslashes($pincode) . "');
    console.log('  destination:', '" . addslashes($destination) . "');
    console.log('  weight:', '" . addslashes($weight) . "');
    console.log('  mode_of_tsp:', '" . addslashes($mode_of_tsp) . "');
    console.log('  waybill_value:', '" . addslashes($waybill_value) . "');
    console.log('  amount:', '" . addslashes($amount) . "');
    console.log('  entry_ts:', '" . addslashes($entry_ts) . "');
    console.log('  username:', '" . addslashes($username) . "');
    </script>";

    $stmt = $conn->prepare("
        INSERT INTO transactions
        (id, entry_type, customer, docket_no, docket_date, pincode, destination, weight, mode_of_tsp, waybill_value, waybill_percent, owner_risk, carrier_risk, oda_chrg, remarks, username, amount, entry_ts, created_at)
        VALUES (NULL, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
    ");

    // Add more detailed error checking
    if ($stmt === false) {
        echo "<script>console.error('❌ Prepare statement failed:', '" . addslashes($conn->error) . "');</script>";
        error_log("Error preparing insert statement: " . $conn->error);
        die("<script>alert('Database prepare error: " . addslashes($conn->error) . "'); window.history.back();</script>");
    }

    error_log("Binding parameters with amount: " . $amount . " and entry_ts: " . $entry_ts);

    $entry_type = 'credit';
    $oda_charges = $_POST['oda_charges'] ?? 0;
    $risk_type = $_POST['risk_charges'];
    $risk_value = $_POST['risk_charges_value'] ?? 0;

    // Set owner_risk and carrier_risk values based on risk type
    $owner_risk_value = ($risk_type === 'Owner Risk') ? $risk_value : 0;
    $carrier_risk_value = ($risk_type === 'Carrier Risk') ? $risk_value : 0;

    $waybill_percent = 0;
    $input_weight = floatval($weight); // This is what you want to store in the DB
    if ($mode_of_tsp === 'Surface') {
        $chargeable_weight = max($input_weight, 3); // Use for amount calculation only
    } else {
        $chargeable_weight = $input_weight;
    }

    $stmt->bind_param("sssssssssssssssss",
        $entry_type,
        $customer,
        $docket_no,
        $docket_date,
        $pincode,
        $destination,
        $original_input_weight, // <-- always use this for DB
        $mode_of_tsp,
        $waybill_value,
        $waybill_percent,
        $owner_risk_value,
        $carrier_risk_value,
        $oda_charges,
        $remarks,
        $username,
        $amount,
        $entry_ts
    );

    echo "<script>console.log('💾 Executing database insertion...');</script>";
    $result = $stmt->execute();

    if ($result) {
        echo "<script>console.log('✅ Database insertion successful! Inserted ID: " . $conn->insert_id . "');</script>";
        error_log("Transaction successfully inserted");
    } else {
        echo "<script>console.error('❌ Database insertion failed:', '" . addslashes($stmt->error) . "');</script>";
        echo "<script>console.error('❌ MySQL Error Code:', " . $stmt->errno . ");</script>";
        error_log("Error executing insert statement: " . $stmt->error);

        // Check if it's a duplicate key error (Error code 1062)
        if ($stmt->errno == 1062) {
            echo "<script>console.error('🚫 UNIQUE CONSTRAINT VIOLATION: The database has a UNIQUE constraint on (docket_no, username) that prevents duplicate entries.');</script>";
            die("<script>
                alert('❌ Database Constraint Error:\\n\\nThe database has a UNIQUE constraint that prevents duplicate docket numbers for the same user.\\n\\nTo allow duplicate entries, the database constraint needs to be removed.\\n\\nError: " . addslashes($stmt->error) . "');
                window.history.back();
            </script>");
        } else {
            die("<script>alert('Database insertion error: " . addslashes($stmt->error) . "'); window.history.back();</script>");
        }
    }
    $stmt->close();

    echo "<script>
    console.log('🎉 PROCESS COMPLETED SUCCESSFULLY');
    alert('✅ Credit Entry successfully saved!');
    window.location.href = 'https://astradigitalsolutions.in/index.php?page=credit-entry';
    </script>";
}

// ========================= BULK EXCEL UPLOAD ========================= //
if ($_SERVER['REQUEST_METHOD'] === 'POST' && (isset($_FILES['excel_file']) || isset($_POST['from_verification']))) {
    error_log("=== STARTING BULK UPLOAD PROCESS ===");
    error_log("File details: " . print_r($_FILES['excel_file'], true));



    // Check if C-Note validation is enabled for bulk upload
    $settings_stmt = $conn->prepare("SELECT cnote_validation_enabled FROM settings WHERE username = ?");
    $settings_stmt->bind_param("s", $username);
    $settings_stmt->execute();
    $settings_result = $settings_stmt->get_result();
    $settings_data = $settings_result->fetch_assoc();
    $bulk_cnote_validation_enabled = $settings_data['cnote_validation_enabled'] ?? 1; // Default to enabled
    $settings_stmt->close();
    error_log("Bulk C-Note validation enabled: " . ($bulk_cnote_validation_enabled ? 'Yes' : 'No'));

    // Check if data comes from verification session or file upload
    if (isset($_POST['from_verification']) && $_POST['from_verification'] === 'true') {
        // Use data from verification session
        if (!isset($_SESSION['verification_results']) || !isset($_SESSION['verification_results']['file_data'])) {
            die("<script>alert('Error: No verification data found. Please verify the file again.'); window.location.href='../index.php?page=credit-entry';</script>");
        }

        $rows = $_SESSION['verification_results']['file_data'];
        error_log("Using verified data from session");
        error_log("Total rows from verification: " . count($rows));

        // Set verification passed flag to skip validation
        $verification_passed = true;
        error_log("✅ DATA FROM VERIFICATION - Skipping all validation checks");

        // Clear the verification data after use
        unset($_SESSION['verification_results']);

    } else {
        // Original file upload process
        require __DIR__ . '/../vendor/autoload.php';

        $file = $_FILES['excel_file']['tmp_name'];
        error_log("Temporary file path: " . $file);

        $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReaderForFile($file);
        error_log("Reader created successfully");

        $spreadsheet = $reader->load($file);
        error_log("Spreadsheet loaded successfully");

        $worksheet = $spreadsheet->getActiveSheet();
        error_log("Active worksheet retrieved");

        $rows = $worksheet->toArray();
        error_log("Total rows in Excel: " . count($rows));
        error_log("First row (headers): " . print_r($rows[0], true));

        // For regular file upload, verification has not passed
        $verification_passed = false;
    }

    try {
        $successCount = 0;
        $errorCount = 0;
        $errorMessages = [];
        $successMessages = [];

        foreach ($rows as $index => $row) {
            if ($index === 0) continue; // Skip Header

            error_log("\n=== PROCESSING ROW " . ($index + 1) . " ===");
            error_log("Raw row data: " . print_r($row, true));

            list($entry_type, $customer, $docket_no, $docket_date, $pincode, $destination, $original_input_weight, $mode_of_tsp, $waybill_value, $remarks, $file_username) = $row;

            error_log("Extracted values:");
            error_log("Entry Type: " . $entry_type);
            error_log("Customer: " . $customer);
            error_log("Docket No: " . $docket_no);
            error_log("Docket Date: " . $docket_date);
            error_log("Pincode: " . $pincode);
            error_log("Destination: " . $destination);
            error_log("Weight: " . $original_input_weight);
            error_log("Mode of TSP: " . $mode_of_tsp);
            error_log("Waybill Value: " . $waybill_value);
            error_log("Remarks: " . $remarks);
            error_log("File Username: " . $file_username);

            if ($file_username !== $username) {
                $errorMsg = "Row $index: Username mismatch. Expected: $username, Got: $file_username";
                error_log($errorMsg);
                $errorMessages[] = $errorMsg;
                $errorCount++;
                continue;
            }

            // Validate CN number and customer allocation (if validation is enabled and not already verified)
            if ($bulk_cnote_validation_enabled && !$verification_passed) {
                error_log("Validating CN number: " . $docket_no);
                $validate_stmt = $conn->prepare("SELECT customer FROM cn_entries WHERE cn_number = ?");
                $validate_stmt->bind_param("s", $docket_no);
                $validate_stmt->execute();
                $validate_result = $validate_stmt->get_result();

                if ($validate_result->num_rows === 0) {
                    $errorMsg = "Row $index: CN Number not found in database.";
                    error_log($errorMsg);
                    $errorMessages[] = $errorMsg;
                    $errorCount++;
                    $validate_stmt->close();
                    continue;
                }
                $cn_data = $validate_result->fetch_assoc();
                error_log("CN data found: " . print_r($cn_data, true));

                if (!empty($cn_data['customer']) && $cn_data['customer'] !== $customer) {
                    $errorMsg = "Row $index: CN Number is already allocated to another customer.";
                    error_log($errorMsg);
                    $errorMessages[] = $errorMsg;
                    $errorCount++;
                    $validate_stmt->close();
                    continue;
                }
                $validate_stmt->close();
            } else {
                error_log("C-Note validation is disabled for bulk upload - skipping CN validation for: " . $docket_no);
            }

            // Check for duplicate docket (only if not already verified)
            if (!$verification_passed) {
                error_log("Checking for duplicate docket: " . $docket_no);
                $trans_check_stmt = $conn->prepare("SELECT docket_no FROM transactions WHERE docket_no = ? AND username = ?");
                $trans_check_stmt->bind_param("ss", $docket_no, $username);
                $trans_check_stmt->execute();
                $trans_check_stmt->store_result();

                if ($trans_check_stmt->num_rows > 0) {
                    $errorMsg = "Row $index: Docket number already used for another shipment.";
                    error_log($errorMsg);
                    $errorMessages[] = $errorMsg;
                    $errorCount++;
                    $trans_check_stmt->close();
                    continue;
                }
                $trans_check_stmt->close();
            }

            // Fetch customer rates
            error_log("Fetching customer rates for: " . $customer);
            $customer_rates_query = "SELECT waybill_percent, owner_risk, carrier_risk FROM customers WHERE short_name='$customer' AND username='$username'";
            $customer_rates_result = mysqli_query($conn, $customer_rates_query);
            $customer_rates = mysqli_fetch_assoc($customer_rates_result);
            error_log("Customer rates: " . print_r($customer_rates, true));

            if (!$customer_rates) {
                $errorMsg = "Row $index: Customer rates not found.";
                error_log($errorMsg);
                $errorMessages[] = $errorMsg;
                $errorCount++;
                continue;
            }

            // Calculate risk values based on risk_type and waybill_value
            $waybill_value = floatval($waybill_value);
            $owner_risk = floatval($customer_rates['owner_risk']);
            $carrier_risk = floatval($customer_rates['carrier_risk']);
            $owner_risk_value = 0;
            $carrier_risk_value = 0;
            if (isset($row[11])) { // risk_type column
                $risk_type = trim($row[11]);
                if ($risk_type === 'Owner Risk') {
                    $owner_risk_value = ($waybill_value * $owner_risk) / 100;
                } elseif ($risk_type === 'Carrier Risk') {
                    $carrier_risk_value = ($waybill_value * $carrier_risk) / 100;
                }
            } else {
                $risk_type = '';
            }

            // Date validation
            error_log("Validating date: " . $docket_date);
            $docket_date = trim($docket_date);
            $separator = (strpos($docket_date, '/') !== false) ? '/' : '-';
            $date_parts = explode($separator, $docket_date);
            error_log("Date parts: " . print_r($date_parts, true));

            if (count($date_parts) === 3) {
                if (strlen($date_parts[2]) === 4) {
                    $month = $date_parts[0];
                    $day = $date_parts[1];
                    $year = $date_parts[2];
                } else if (strlen($date_parts[0]) === 4) {
                    $year = $date_parts[0];
                    $month = $date_parts[1];
                    $day = $date_parts[2];
                }
                $docket_date = sprintf('%04d-%02d-%02d', (int)$year, (int)$month, (int)$day);
                error_log("Formatted date: " . $docket_date);
            } else {
                $errorMsg = "Row $index: Invalid date format in '$docket_date'. Expected format: MM-DD-YYYY";
                error_log($errorMsg);
                $errorMessages[] = $errorMsg;
                $errorCount++;
                continue;
            }

            // Fetch Zone
            error_log("Fetching zone for pincode: " . $pincode);
            $zone_query = "SELECT zone, ts_zone, pr_zone FROM pincode_data WHERE pincode=?";
            $zone_stmt = $conn->prepare($zone_query);
            if (!$zone_stmt) {
                error_log("Error preparing zone statement: " . $conn->error);
                die("<script>alert('Error: Database error occurred!'); window.history.back();</script>");
            }

            $zone_stmt->bind_param("s", $pincode);
            $zone_stmt->execute();
            $zone_result = $zone_stmt->get_result();

            if ($zone_row = $zone_result->fetch_assoc()) {
                $zone = $zone_row['zone'];
                $ts_zone = $zone_row['ts_zone'];
                $pr_zone = $zone_row['pr_zone'];
                error_log("Zone data found: " . print_r($zone_row, true));

                if (empty($ts_zone)) {
                    error_log("Warning: ts_zone is empty for pincode: " . $pincode);
                    $ts_zone = $zone;
                    error_log("Using regular zone as ts_zone: " . $ts_zone);
                }

                if (empty($zone)) {
                    $errorMsg = "Row $index: No zone found for pincode " . $pincode;
                    error_log($errorMsg);
                    die("<script>alert('Error: " . $errorMsg . "'); window.history.back();</script>");
                }
            } else {
                $errorMsg = "Row $index: No zone data found for pincode " . $pincode;
                error_log($errorMsg);
                die("<script>alert('Error: " . $errorMsg . "'); window.history.back();</script>");
            }
            $zone_stmt->close();

            // Log zone information
            error_log("Zone Information:");
            error_log("Pincode: " . $pincode);
            error_log("Zone: " . $zone);
            error_log("TS Zone: " . $ts_zone);
            error_log("PR Zone: " . $pr_zone);
            error_log("Mode of TSP: " . $mode_of_tsp);

            // Calculate amount using rate_master
            error_log("Calculating amount using rate_master");
            error_log("Parameters - Customer: $customer, Mode: $rate_mode, Zone: $zone, PR Zone: $pr_zone");

            $amount = 0;
            $rate_mode = $mode_of_tsp === 'Air Cargo' ? 'Express' : $mode_of_tsp;

            // Use pr_zone for Premium mode, zone for others
            if ($mode_of_tsp === 'Premium') {
                $rate_query = "SELECT * FROM rate_master WHERE short_name=? AND mode_of_tsp=? AND UPPER(pr_zone)=UPPER(?)";
                $rate_stmt = $conn->prepare($rate_query);
                $rate_stmt->bind_param("sss", $customer, $rate_mode, $pr_zone);
            } else {
                $rate_query = "SELECT * FROM rate_master WHERE short_name=? AND mode_of_tsp=? AND UPPER(zone)=UPPER(?)";
                $rate_stmt = $conn->prepare($rate_query);
                $rate_stmt->bind_param("sss", $customer, $rate_mode, $zone);
            }
            $rate_stmt->execute();
            $rate_result = $rate_stmt->get_result();

            if ($rate_row = $rate_result->fetch_assoc()) {
                error_log("Rate data found for amount calculation: " . print_r($rate_row, true));
                $weight = floatval($original_input_weight);

                if ($rate_mode === 'Surface') {
                    // For Surface mode
                    $min_weight = floatval($rate_row['above_3kg']); // minimum weight
                    $actual_weight = max($weight, $min_weight);
                    $rounded_weight = ceil($actual_weight);

                    // Select rate based on weight slabs for Surface mode
                    if ($rounded_weight <= 10) {
                        $rate_used = floatval($rate_row['up_to_0250']); // 1-10 kg rate
                        error_log("Surface mode - Using up_to_0250 rate: " . $rate_used);
                    } elseif ($rounded_weight <= 20) {
                        $rate_used = floatval($rate_row['up_to_0500']); // 11-20 kg rate
                        error_log("Surface mode - Using up_to_0500 rate: " . $rate_used);
                    } else {
                        $rate_used = floatval($rate_row['addl_500gm']); // Above 20 kg rate
                        error_log("Surface mode - Using addl_500gm rate: " . $rate_used);
                    }

                    $amount = $rounded_weight * $rate_used;
                    error_log("Surface mode - Final amount: " . $amount);
                } else if ($rate_mode === 'Premium') {
                    // For Premium mode
                    if ($weight <= 0.250) {
                        // First 250 grams, no rounding
                        $amount = floatval($rate_row['up_to_0250']);
                        error_log("Premium mode - Using up_to_0250 rate: " . $amount);
                    } else {
                        // Round weight to nearest 500gms up to 10kg
                        if ($weight <= 10) {
                            $weight = ceil($weight * 2) / 2; // Round to nearest 500gms
                            if ($weight <= 0.500) {
                                $amount = floatval($rate_row['up_to_0500']);
                                error_log("Premium mode - Using up_to_0500 rate: " . $amount);
                            } else {
                                $amount = floatval($rate_row['up_to_0500']) + (($weight - 0.500) / 0.500) * floatval($rate_row['addl_500gm']);
                                error_log("Premium mode - Calculated amount with addl_500gm: " . $amount);
                            }
                        } else {
                            // Above 10kg - apply flat per kg rate
                            $amount = ceil($weight) * floatval($rate_row['above_3kg']);
                            error_log("Premium mode - Using above_3kg rate: " . $amount);
                        }
                    }
                } else {
                    // For Express mode
                    if ($weight <= 0.250) {
                        // First 250 grams, no rounding
                        $amount = floatval($rate_row['up_to_0250']);
                        error_log("Express mode - Using up_to_0250 rate: " . $amount);
                    } else {
                        // Round weight only if above 250 grams
                        $weight = ceil($weight * 2) / 2;

                        if ($weight <= 0.500) {
                            $amount = floatval($rate_row['up_to_0500']);
                            error_log("Express mode - Using up_to_0500 rate: " . $amount);
                        } elseif ($weight <= 3) {
                            $amount = floatval($rate_row['up_to_0500']) + (($weight - 0.500) / 0.500) * floatval($rate_row['addl_500gm']);
                            error_log("Express mode - Calculated amount with addl_500gm: " . $amount);
                        } else {
                            // Above 3 kg calculation
                            $amount = $weight * floatval($rate_row['above_3kg']);
                            error_log("Express mode - Using above_3kg rate: " . $amount);
                        }
                    }
                }
            } else {
                error_log("No rate found for amount calculation - customer: $customer, mode: $rate_mode, zone: $zone");
                die("<script>alert('Error: Rate not found for amount calculation! Please check if rates are configured for " . $rate_mode . " mode in the selected zone (" . $zone . ") for customer " . $customer . ".'); window.history.back();</script>");
            }
            $rate_stmt->close();

            // Calculate entry_ts using ts_rate_master
            $entry_ts = 0;
            $ts_rate_query = "SELECT * FROM ts_rate_master WHERE username=? AND mode_of_tsp=? AND UPPER(zone)=UPPER(?)";
            $ts_rate_stmt = $conn->prepare($ts_rate_query);

            if (!$ts_rate_stmt) {
                error_log("Error preparing ts_rate statement: " . $conn->error);
                die("<script>alert('Error: Database error occurred!'); window.history.back();</script>");
            }

            $ts_rate_stmt->bind_param("sss", $username, $mode_of_tsp, $ts_zone);
            error_log("Executing TS rate query with username: $username, mode: $mode_of_tsp, zone: $ts_zone");
            $ts_rate_stmt->execute();
            $ts_rate_result = $ts_rate_stmt->get_result();

            if ($ts_rate_row = $ts_rate_result->fetch_assoc()) {
                error_log("TS Rate data found: " . print_r($ts_rate_row, true));
                $weight = floatval($original_input_weight);

                // Apply minimum weight check first
                $min_weight = floatval($ts_rate_row['min_weight']);
                if ($weight < $min_weight) {
                    $weight = $min_weight;
                    error_log("Applied minimum weight: " . $min_weight);
                }

                if ($mode_of_tsp === 'Surface' || $mode_of_tsp === 'Air Cargo') {
                    // Round weight to nearest 1kg
                    $rounded_weight = ceil($weight);
                    error_log($mode_of_tsp . " mode - Rounded weight: " . $rounded_weight);

                    if ($rounded_weight <= 5.000) {
                        $entry_ts = $rounded_weight * floatval($ts_rate_row['up_to_5kg']);
                        error_log("Using up_to_5kg rate for entry_ts: " . $entry_ts);
                    } elseif ($rounded_weight <= 10.000) {
                        $entry_ts = $rounded_weight * floatval($ts_rate_row['up_to_10kg']);
                        error_log("Using up_to_10kg rate for entry_ts: " . $entry_ts);
                    } elseif ($rounded_weight <= 25.000) {
                        $entry_ts = $rounded_weight * floatval($ts_rate_row['up_to_25kg']);
                        error_log("Using up_to_25kg rate for entry_ts: " . $entry_ts);
                    } elseif ($rounded_weight <= 50.000) {
                        $entry_ts = $rounded_weight * floatval($ts_rate_row['up_to_50kg']);
                        error_log("Using up_to_50kg rate for entry_ts: " . $entry_ts);
                    } else {
                        $entry_ts = $rounded_weight * floatval($ts_rate_row['above_50kg']);
                        error_log("Using above_50kg rate for entry_ts: " . $entry_ts);
                    }
                } elseif ($mode_of_tsp === 'Premium') {
                    if ($weight <= 0.250) {
                        $entry_ts = floatval($ts_rate_row['up_to_0250']);
                        error_log("Premium mode - Using up_to_0250 rate for entry_ts: " . $entry_ts);
                    } elseif ($weight <= 0.500) {
                        $entry_ts = floatval($ts_rate_row['up_to_0500']);
                        error_log("Premium mode - Using up_to_0500 rate for entry_ts: " . $entry_ts);
                    } elseif ($weight <= 10.000) {
                        $rounded_weight = ceil($weight * 2) / 2;
                        $base_rate = floatval($ts_rate_row['up_to_0500']);
                        $additional_weight = ($rounded_weight - 0.500) * 2;
                        $entry_ts = $base_rate + ($additional_weight * floatval($ts_rate_row['addl_500gm']));
                        error_log("Premium mode - Calculated entry_ts with addl_500gm: " . $entry_ts);
                    } else {
                        $rounded_weight = ceil($weight);
                        $entry_ts = $rounded_weight * floatval($ts_rate_row['above_50kg']);
                        error_log("Premium mode - Using above_50kg rate for entry_ts: " . $entry_ts);
                    }
                } else {
                    // Express mode
                    if ($weight <= 0.100) {
                        $entry_ts = floatval($ts_rate_row['up_to_0100']);
                        error_log("Express mode - Using up_to_0100 rate for entry_ts: " . $entry_ts);
                    } elseif ($weight <= 0.250) {
                        $entry_ts = floatval($ts_rate_row['up_to_0250']);
                        error_log("Express mode - Using up_to_0250 rate for entry_ts: " . $entry_ts);
                    } elseif ($weight <= 0.500) {
                        $entry_ts = floatval($ts_rate_row['up_to_0500']);
                        error_log("Express mode - Using up_to_0500 rate for entry_ts: " . $entry_ts);
                    } elseif ($weight <= 3.000) {
                        $base_rate = floatval($ts_rate_row['up_to_0500']);
                        $additional_weight = ceil(($weight - 0.500) * 2);
                        $entry_ts = $base_rate + ($additional_weight * floatval($ts_rate_row['addl_500gm']));
                        error_log("Express mode - Calculated entry_ts with addl_500gm: " . $entry_ts);
                    } elseif ($weight <= 5.000) {
                        $entry_ts = $weight * floatval($ts_rate_row['up_to_5kg']);
                        error_log("Express mode - Using up_to_5kg rate for entry_ts: " . $entry_ts);
                    } elseif ($weight <= 10.000) {
                        $entry_ts = $weight * floatval($ts_rate_row['up_to_10kg']);
                        error_log("Express mode - Using up_to_10kg rate for entry_ts: " . $entry_ts);
                    } elseif ($weight <= 25.000) {
                        $entry_ts = $weight * floatval($ts_rate_row['up_to_25kg']);
                        error_log("Express mode - Using up_to_25kg rate for entry_ts: " . $entry_ts);
                    } elseif ($weight <= 50.000) {
                        $entry_ts = $weight * floatval($ts_rate_row['up_to_50kg']);
                        error_log("Express mode - Using up_to_50kg rate for entry_ts: " . $entry_ts);
                    } else {
                        $entry_ts = $weight * floatval($ts_rate_row['above_50kg']);
                        error_log("Express mode - Using above_50kg rate for entry_ts: " . $entry_ts);
                    }
                }
            } else {
                error_log("No TS rate found for username: $username, mode: $mode_of_tsp, zone: $ts_zone");
                die("<script>alert('Error: TS Rate not found for given parameters! Please check if rates are configured for " . $mode_of_tsp . " mode in the selected zone (" . $ts_zone . ").'); window.history.back();</script>");
            }
            $ts_rate_stmt->close();

            // Insert into Transactions Table with all calculated values
            $stmt = $conn->prepare("
                INSERT INTO transactions
                (entry_type, customer, docket_no, docket_date, pincode, destination, weight, mode_of_tsp, waybill_value, waybill_percent, owner_risk, carrier_risk, oda_chrg, remarks, username, amount, entry_ts)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $entry_type = 'credit';
            $oda_charges = 0; // Default for bulk upload
            $waybill_percent = 0;

            $input_weight = floatval($weight); // This is what you want to store in the DB
            if ($mode_of_tsp === 'Surface') {
                $chargeable_weight = max($input_weight, 3); // Use for amount calculation only
            } else {
                $chargeable_weight = $input_weight;
            }

            $stmt->bind_param("sssssssssssssssss",
                $entry_type,
                $customer,
                $docket_no,
                $docket_date,
                $pincode,
                $destination,
                $original_input_weight, // <-- always use this for DB
                $mode_of_tsp,
                $waybill_value,
                $waybill_percent,
                $owner_risk_value,
                $carrier_risk_value,
                $oda_charges,
                $remarks,
                $username,
                $amount,
                $entry_ts
            );

            if (!$stmt->execute()) {
                $errorMsg = "Row $index: Database insertion failed.";
                error_log($errorMsg);
                $errorMessages[] = $errorMsg;
                $errorCount++;
            } else {
                $successMsg = "Row $index: Successfully processed.";
                error_log($successMsg);
                $successMessages[] = $successMsg;
                $successCount++;
            }
            $stmt->close();
        }

        $_SESSION['successMessages'] = $successMessages;
        $_SESSION['errorMessages'] = $errorMessages;
        header('Location: error_page.php');
        exit();

    } catch (Exception $e) {
        error_log("Error in bulk upload processing: " . $e->getMessage());
        die("<script>alert('Error processing bulk upload: " . $e->getMessage() . "'); window.history.back();</script>");
    }
}

$conn->close();
error_log("=== Completed process_credit_entry.php execution ===");
?>
