<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Debug log function
function debug_log($message) {
    $_SESSION['debug'] = isset($_SESSION['debug']) ? $_SESSION['debug'] . "\n" . $message : $message;
}

include 'db_connect.php';

if (!isset($_SESSION['username'])) {
    die("<script>alert('You must be logged in!'); window.location.href='../login.php';</script>");
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    debug_log("Invalid request method: " . $_SERVER['REQUEST_METHOD']);
    header('Location: dashboard.php?page=freight_rate-calculator');
    exit;
}

// Debug POST data
debug_log("POST data received: " . print_r($_POST, true));

// Get form data
$pincode = $_POST['pincode'] ?? '';
$weight = floatval($_POST['weight'] ?? 0);
$margin_percentage = floatval($_POST['margin_percentage'] ?? 0);

if (empty($pincode) || $weight <= 0 || $margin_percentage < 0) {
    $_SESSION['error'] = "Please provide valid pincode, weight, and margin percentage.";
    debug_log("Validation failed - Pincode: $pincode, Weight: $weight, Margin: $margin_percentage");
    header('Location: index.php?page=freight_rate-calculator');
    exit;
}

try {
    // Check database connection
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    debug_log("Database connection successful");

    // Check table structure
    $tableStructure = $conn->query("SHOW COLUMNS FROM ts_rate_master");
    if ($tableStructure) {
        $columns = [];
        while ($column = $tableStructure->fetch_assoc()) {
            $columns[] = $column['Field'];
        }
        debug_log("ts_rate_master columns: " . print_r($columns, true));
    } else {
        debug_log("Failed to get table structure: " . $conn->error);
    }

    // Get zone from pincode
    $stmt = $conn->prepare("SELECT ts_zone FROM pincode_data WHERE pincode = ?");
    if (!$stmt) {
        throw new Exception("Failed to prepare zone query: " . $conn->error);
    }

    $stmt->bind_param("s", $pincode);
    if (!$stmt->execute()) {
        throw new Exception("Failed to execute zone query: " . $stmt->error);
    }

    $result = $stmt->get_result();
    if (!$result->num_rows) {
        throw new Exception("No zone found for pincode: " . $pincode);
    }

    $row = $result->fetch_assoc();
    $zone = $row['ts_zone'];
    debug_log("Found zone: $zone for pincode: $pincode");
    $stmt->close();

    // Initialize results array
    $calculation_results = [];
    $modes = ['Express', 'Air Cargo', 'Premium', 'Surface'];

    foreach ($modes as $mode) {
        debug_log("Processing mode: $mode");
        // Get rates for each mode
        $stmt = $conn->prepare("SELECT * FROM ts_rate_master WHERE mode_of_tsp = ? AND zone = ?");
        if (!$stmt) {
            throw new Exception("Failed to prepare rates query for $mode: " . $conn->error);
        }

        $stmt->bind_param("ss", $mode, $zone);
        if (!$stmt->execute()) {
            throw new Exception("Failed to execute rates query for $mode: " . $stmt->error);
        }

        $result = $stmt->get_result();
        if (!$result->num_rows) {
            debug_log("No rates found for mode: $mode and zone: $zone");
            continue;
        }

        $rates = $result->fetch_assoc();
        debug_log("Found rates for $mode: " . print_r($rates, true));
        $stmt->close();

        // Calculate rate based on weight
        $weightInGrams = $weight * 1000;
        $baseRate = 0;

        if ($mode === 'Premium') {
            // Premium rate calculation
            if ($weightInGrams <= 250) {
                $baseRate = floatval($rates['up_to_0250']);
            } elseif ($weightInGrams <= 500) {
                $baseRate = floatval($rates['up_to_0500']);
            } elseif ($weightInGrams <= 10000) {
                $additionalSlabs = ceil(($weightInGrams - 500) / 500);
                $baseRate = floatval($rates['up_to_0500']) + ($additionalSlabs * floatval($rates['addl_500gm']));
            } else {
                $baseRate = $weight * floatval($rates['above_50kg']);
            }
        } elseif ($mode === 'Express') {
            // Express rate calculation - set higher weight slabs to 0
            if ($weightInGrams <= 100) {
                $baseRate = floatval($rates['up_to_0100']);
            } elseif ($weightInGrams <= 250) {
                $baseRate = floatval($rates['up_to_0250']);
            } elseif ($weightInGrams <= 500) {
                $baseRate = floatval($rates['up_to_0500']);
            } elseif ($weightInGrams <= 3000) {
                $additionalSlabs = ceil(($weightInGrams - 500) / 500);
                $baseRate = floatval($rates['up_to_0500']) + ($additionalSlabs * floatval($rates['addl_500gm']));
            } else {
                $baseRate = 0; // Set to 0 for weights above 3kg
            }
        } elseif ($mode === 'Air Cargo') {
            // Air Cargo rate calculation with MCW check
            $actualWeight = $weight;
            $minWeight = floatval($rates['min_weight']);
            
            // Use MCW if actual weight is less than minimum weight
            $calculationWeight = max($actualWeight, $minWeight);
            
            debug_log("Air Cargo - Using weight for calculation: $calculationWeight kg (MCW: $minWeight kg)");

            // Calculate base rate using weight slabs
            if ($calculationWeight <= 5) {
                $baseRate = floatval($rates['up_to_5kg']);
            } elseif ($calculationWeight <= 10) {
                $baseRate = floatval($rates['up_to_10kg']);
            } elseif ($calculationWeight <= 25) {
                $baseRate = floatval($rates['up_to_25kg']);
            } elseif ($calculationWeight <= 50) {
                $baseRate = floatval($rates['up_to_50kg']);
            } else {
                $baseRate = floatval($rates['above_50kg']);
            }

            // Apply the base rate to the calculation weight
            $baseRate = $baseRate * $calculationWeight;
            
            debug_log("Air Cargo - Base rate per kg: " . ($baseRate / $calculationWeight) . ", Total base rate: $baseRate");
        } else {
            // Surface rate calculation with MCW check
            $actualWeight = $weight;
            $minWeight = floatval($rates['min_weight']);
            
            // Use MCW if actual weight is less than minimum weight
            $calculationWeight = max($actualWeight, $minWeight);
            
            debug_log("Surface - Using weight for calculation: $calculationWeight kg (MCW: $minWeight kg)");

            // Calculate base rate using weight slabs
            if ($calculationWeight <= 5) {
                $baseRate = floatval($rates['up_to_5kg']);
            } elseif ($calculationWeight <= 10) {
                $baseRate = floatval($rates['up_to_10kg']);
            } elseif ($calculationWeight <= 25) {
                $baseRate = floatval($rates['up_to_25kg']);
            } elseif ($calculationWeight <= 50) {
                $baseRate = floatval($rates['up_to_50kg']);
            } else {
                $baseRate = floatval($rates['above_50kg']);
            }

            // Apply the base rate to the calculation weight
            $baseRate = $baseRate * $calculationWeight;
            
            debug_log("Surface - Base rate per kg: " . ($baseRate / $calculationWeight) . ", Total base rate: $baseRate");
        }

        debug_log("$mode - Base rate calculated: $baseRate for weight: $weight kg");

        // Apply margin
        $rateWithMargin = $baseRate * (1 + ($margin_percentage / 100));
        
        // Calculate additional charges
        $fsc = $rateWithMargin * 0.15;
        $subtotal = $rateWithMargin + $fsc;
        $gst = $subtotal * 0.18;
        $cnCharges = 10;
        $total = $subtotal + $gst + $cnCharges;

        debug_log("$mode - Final calculations: Rate with margin: $rateWithMargin, FSC: $fsc, GST: $gst, Total: $total");

        // Store results
        $calculation_results[$mode] = [
            'base_rate' => $baseRate,
            'rate' => $rateWithMargin,
            'fsc' => $fsc,
            'gst' => $gst,
            'cn_charges' => $cnCharges,
            'total' => $total
        ];
    }

    // Store results in session and redirect
    $_SESSION['calculation_results'] = $calculation_results;
    $_SESSION['last_calculation'] = [
        'pincode' => $pincode,
        'weight' => $weight,
        'margin_percentage' => $margin_percentage,
        'timestamp' => time()
    ];
    debug_log("Calculation completed successfully. Redirecting back to calculator.");
    header('Location: dashboard.php?page=freight_rate-calculator');
    exit;

} catch (Exception $e) {
    $_SESSION['error'] = $e->getMessage();
    debug_log("Error occurred: " . $e->getMessage());
    // Clear any previous results on error
    unset($_SESSION['calculation_results']);
    unset($_SESSION['last_calculation']);
    header('Location: dashboard.php?page=freight_rate-calculator');
    exit;
} finally {
    if (isset($stmt)) {
        $stmt->close();
    }
    if (isset($conn)) {
        $conn->close();
    }
}
?> 