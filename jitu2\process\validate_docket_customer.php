<?php
session_start();
include '../db_connect.php';

// Enable error logging
error_log("Starting validate_docket_customer.php");

if (!isset($_SESSION['username'])) {
    error_log("User not logged in");
    die(json_encode(['valid' => false, 'error' => 'Not logged in']));
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $docket_no = $_POST['docket_no'];
    $customer = $_POST['customer'];
    $username = $_SESSION['username'];

    error_log("Received POST data - Docket: $docket_no, Customer: $customer");

    // Check if C-Note validation is enabled for this user
    $settings_stmt = $conn->prepare("SELECT cnote_validation_enabled FROM settings WHERE username = ?");
    $settings_stmt->bind_param("s", $username);
    $settings_stmt->execute();
    $settings_result = $settings_stmt->get_result();
    $settings_data = $settings_result->fetch_assoc();
    $cnote_validation_enabled = $settings_data['cnote_validation_enabled'] ?? 1; // Default to enabled
    $settings_stmt->close();

    if ($cnote_validation_enabled) {
        // Validate docket number exists in cn_entries and matches with selected customer
        $query = "SELECT customer FROM cn_entries WHERE cn_number = ?";
        error_log("Executing query: $query with docket_no: $docket_no");

        $stmt = $conn->prepare($query);
        $stmt->bind_param("s", $docket_no);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows === 0) {
            error_log("CN number not found in database - allowing entry");
            echo json_encode(['valid' => true]); // Allow if CN doesn't exist
        } else {
            $cn_data = $result->fetch_assoc();
            error_log("Found CN data - Raw data: " . print_r($cn_data, true));
            error_log("Found CN data - Stored Customer: {$cn_data['customer']}, Input Customer: $customer");

            // If CN exists but has no customer allocated, allow entry
            if (empty($cn_data['customer']) || $cn_data['customer'] === null) {
                error_log("CN exists but not allocated to any customer - allowing entry");
                echo json_encode(['valid' => true]);
            } else {
                // If CN is allocated to a customer, check if it matches
                if ($cn_data['customer'] === $customer) {
                    error_log("Customer match confirmed");
                    echo json_encode(['valid' => true]);
                } else {
                    error_log("Customer mismatch detected - stored: {$cn_data['customer']}, input: $customer");
                    echo json_encode(['valid' => false, 'error' => 'CN Number is already allocated to another customer']);
                }
            }
        }
        $stmt->close();
    } else {
        // Validation is disabled, always return valid
        error_log("C-Note validation is disabled - allowing entry");
        echo json_encode(['valid' => true]);
    }
    $stmt->close();
} else {
    error_log("Invalid request method: " . $_SERVER['REQUEST_METHOD']);
    echo json_encode(['valid' => false, 'error' => 'Invalid request method']);
}

$conn->close();
error_log("Finished validate_docket_customer.php");
?> 