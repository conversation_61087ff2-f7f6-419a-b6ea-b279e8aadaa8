<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
session_start();
include '../db_connect.php';

header('Content-Type: application/json');

if (!isset($_SESSION['username'])) {
    die(json_encode(['success' => false, 'error' => 'Not logged in']));
}

$username = $_SESSION['username'];
$weight = isset($_GET['weight']) ? floatval($_GET['weight']) : 0;
$mode_of_tsp = isset($_GET['mode_of_tsp']) ? $_GET['mode_of_tsp'] : '';
$pincode = isset($_GET['pincode']) ? $_GET['pincode'] : '';

if (!$weight || !$mode_of_tsp || !$pincode) {
    die(json_encode(['success' => false, 'error' => 'Missing required parameters']));
}

// Get zone from pincode
$zone_query = "SELECT ts_zone FROM pincode_data WHERE pincode = ?";
$zone_stmt = $conn->prepare($zone_query);
$zone_stmt->bind_param("s", $pincode);
$zone_stmt->execute();
$zone_result = $zone_stmt->get_result();
$zone = '';

if ($zone_row = $zone_result->fetch_assoc()) {
    $zone = $zone_row['ts_zone'];
} else {
    die(json_encode(['success' => false, 'error' => 'Zone not found for pincode']));
}
$zone_stmt->close();

// Get rates from ts_rate_master
$rate_query = "SELECT * FROM ts_rate_master WHERE username = ? AND mode_of_tsp = ? AND zone = ?";
$rate_stmt = $conn->prepare($rate_query);
$rate_stmt->bind_param("sss", $username, $mode_of_tsp, $zone);
$rate_stmt->execute();
$rate_result = $rate_stmt->get_result();

if ($rate_row = $rate_result->fetch_assoc()) {
    $amount = 0;
    
    // Express rate calculation based on weight slabs
    if ($weight <= 0.100) {
        // Up to 100 grams - flat rate
        $amount = floatval($rate_row['up_to_0100']);
    } elseif ($weight <= 0.250) {
        // Up to 250 grams - flat rate
        $amount = floatval($rate_row['up_to_0250']);
    } elseif ($weight <= 0.500) {
        // Up to 500 grams - flat rate
        $amount = floatval($rate_row['up_to_0500']);
    } elseif ($weight <= 3.000) {
        // Above 500 grams to 3 kg - base rate + additional 500gm slabs
        $base_rate = floatval($rate_row['up_to_0500']);
        $additional_weight = ceil(($weight - 0.500) * 2); // Round up to nearest 500gm
        $amount = $base_rate + ($additional_weight * floatval($rate_row['addl_500gm']));
    } elseif ($weight <= 5.000) {
        // 3 kg to 5 kg - flat per kg rate
        $amount = $weight * floatval($rate_row['up_to_5kg']);
    } elseif ($weight <= 10.000) {
        // 5 kg to 10 kg - flat per kg rate
        $amount = $weight * floatval($rate_row['up_to_10kg']);
    } elseif ($weight <= 25.000) {
        // 10 kg to 25 kg - flat per kg rate
        $amount = $weight * floatval($rate_row['up_to_25kg']);
    } elseif ($weight <= 50.000) {
        // 25 kg to 50 kg - flat per kg rate
        $amount = $weight * floatval($rate_row['up_to_50kg']);
    } else {
        // Above 50 kg - flat per kg rate
        $amount = $weight * floatval($rate_row['above_50kg']);
    }

    // Apply minimum weight check
    $min_weight = floatval($rate_row['min_weight']);
    if ($weight < $min_weight) {
        $weight = $min_weight;
    }

    echo json_encode(['success' => true, 'amount' => $amount]);
} else {
    echo json_encode(['success' => false, 'error' => 'Rate not found for given parameters']);
}

$rate_stmt->close();
$conn->close();
?> 