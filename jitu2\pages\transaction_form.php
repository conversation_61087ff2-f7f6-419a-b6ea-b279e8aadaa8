<?php
// This file is included by index.php, so session and db connection are already available
$username = $_SESSION['username'];
$edit_mode = false;
$transaction = null;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Sanitize and validate input
        $entry_type = $_POST['entry_type'] ?? '';
        $customer = $_POST['customer'] ?? '';
        $docket_no = $_POST['docket_no'] ?? '';
        $docket_date = $_POST['docket_date'] ?? '';
        $pincode = $_POST['pincode'] ?? '';
        $destination = $_POST['destination'] ?? '';
        $weight = floatval($_POST['weight'] ?? 0);
        $mode_of_tsp = $_POST['mode_of_tsp'] ?? '';
        $waybill_value = floatval($_POST['waybill_value'] ?? 0);
        $remarks = $_POST['remarks'] ?? '';
        $amount = floatval($_POST['amount'] ?? 0);
        $entry_ts = floatval($_POST['entry_ts'] ?? 0);
        $waybill_percent = floatval($_POST['waybill_percent'] ?? 0);
        $oda_chrg = floatval($_POST['oda_chrg'] ?? 0);
        $owner_risk = floatval($_POST['owner_risk'] ?? 0);
        $carrier_risk = floatval($_POST['carrier_risk'] ?? 0);
        $payment_status = $_POST['payment_status'] ?? 'Pending';
        $payment_received_date = $_POST['payment_received_date'] ?: null;
        $waybill = $_POST['waybill'] ?? '';
        $mobile1 = $_POST['mobile1'] ?? '';
        $mobile2 = $_POST['mobile2'] ?? '';

        if (isset($_POST['id']) && !empty($_POST['id'])) {
            // Update existing transaction
            $id = intval($_POST['id']);
            $sql = "UPDATE transactions SET
                entry_type = ?, customer = ?, docket_no = ?, docket_date = ?, pincode = ?,
                destination = ?, weight = ?, mode_of_tsp = ?, waybill_value = ?, remarks = ?,
                amount = ?, entry_ts = ?, waybill_percent = ?, oda_chrg = ?, owner_risk = ?,
                carrier_risk = ?, payment_status = ?, payment_received_date = ?, waybill = ?,
                mobile1 = ?, mobile2 = ?
                WHERE id = ? AND username = ?";

            $stmt = $conn->prepare($sql);
            $stmt->bind_param("ssssssdsssdddddsssssis",
                $entry_type, $customer, $docket_no, $docket_date, $pincode,
                $destination, $weight, $mode_of_tsp, $waybill_value, $remarks,
                $amount, $entry_ts, $waybill_percent, $oda_chrg, $owner_risk,
                $carrier_risk, $payment_status, $payment_received_date, $waybill,
                $mobile1, $mobile2, $id, $username);

            if ($stmt->execute()) {
                $_SESSION['success_message'] = "Transaction updated successfully!";
            } else {
                $_SESSION['error_message'] = "Error updating transaction: " . $stmt->error;
            }
        } else {
            // Insert new transaction
            $sql = "INSERT INTO transactions
                (entry_type, customer, docket_no, docket_date, pincode, destination, weight,
                mode_of_tsp, waybill_value, remarks, amount, entry_ts, waybill_percent,
                oda_chrg, owner_risk, carrier_risk, payment_status, payment_received_date,
                waybill, mobile1, mobile2, username, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

            $stmt = $conn->prepare($sql);
            $stmt->bind_param("ssssssdsssdddddsssssss",
                $entry_type, $customer, $docket_no, $docket_date, $pincode,
                $destination, $weight, $mode_of_tsp, $waybill_value, $remarks,
                $amount, $entry_ts, $waybill_percent, $oda_chrg, $owner_risk,
                $carrier_risk, $payment_status, $payment_received_date, $waybill,
                $mobile1, $mobile2, $username);

            if ($stmt->execute()) {
                $_SESSION['success_message'] = "Transaction created successfully!";
            } else {
                $_SESSION['error_message'] = "Error creating transaction: " . $stmt->error;
            }
        }

        $stmt->close();
        header("Location: index.php?page=transaction_form.php");
        exit();

    } catch (Exception $e) {
        $_SESSION['error_message'] = "Error: " . $e->getMessage();
    }
}

// Handle edit mode
if (isset($_GET['edit']) && !empty($_GET['edit'])) {
    $edit_id = intval($_GET['edit']);
    $stmt = $conn->prepare("SELECT * FROM transactions WHERE id = ? AND username = ?");
    $stmt->bind_param("is", $edit_id, $username);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $transaction = $result->fetch_assoc();
        $edit_mode = true;
    } else {
        $_SESSION['error_message'] = "Transaction not found or access denied.";
    }
    $stmt->close();
}
?>

<!-- Inline CSS for Transaction Form -->
<style>
        :root {
            --primary-blue: #2196F3;
            --light-blue: #E3F2FD;
            --hover-blue: #1976D2;
            --text-dark: #2c3e50;
            --border-color: #e0e0e0;
            --background: #F8FAFC;
            --success-green: #4CAF50;
            --danger-red: #f44336;
        }

        .form-container {
            padding: 2rem;
            margin: 0 auto;
            max-width: 1200px;
            position: relative;
        }

        .form-header {
            background: linear-gradient(135deg, var(--primary-blue) 0%, #1976D2 100%);
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .form-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .form-subtitle {
            font-size: 0.9rem;
            opacity: 0.9;
            margin: 0.5rem 0 0 0;
        }

        .form-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.05);
            overflow: hidden;
        }

        .form-body {
            padding: 2rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-label {
            font-weight: 600;
            color: var(--text-dark);
            font-size: 0.9rem;
        }

        .form-input, .form-select, .form-textarea {
            padding: 0.75rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: var(--background);
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--primary-blue);
            background: white;
            box-shadow: 0 0 0 4px rgba(33, 150, 243, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .form-actions {
            background: #f8f9fa;
            padding: 1.5rem 2rem;
            border-top: 1px solid var(--border-color);
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.95rem;
        }

        .btn-primary {
            background: var(--primary-blue);
            color: white;
        }

        .btn-primary:hover {
            background: var(--hover-blue);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-1px);
        }

        .alert {
            padding: 1rem 1.5rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 1024px) {
            .form-container {
                padding: 1rem;
            }

            .form-header {
                padding: 1rem;
            }
        }

        @media (max-width: 768px) {
            .form-container {
                padding: 1rem;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .form-actions {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>

<!-- Transaction Form Component -->
<div class="form-container">
        <!-- Header Section -->
        <div class="form-header">
            <h1 class="form-title">
                <i class="fas fa-<?php echo $edit_mode ? 'edit' : 'plus-circle'; ?>"></i>
                <?php echo $edit_mode ? 'Edit' : 'Add New'; ?> Transaction
            </h1>
            <p class="form-subtitle">
                <?php echo $edit_mode ? 'Update transaction details' : 'Create a new transaction entry'; ?> - User: <?php echo htmlspecialchars($username); ?>
            </p>
        </div>

        <!-- Success/Error Messages -->
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <?php echo htmlspecialchars($_SESSION['success_message']); ?>
            </div>
            <?php unset($_SESSION['success_message']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                <?php echo htmlspecialchars($_SESSION['error_message']); ?>
            </div>
            <?php unset($_SESSION['error_message']); ?>
        <?php endif; ?>

        <!-- Transaction Form -->
        <div class="form-card">
            <form method="POST" action="">
                <?php if ($edit_mode): ?>
                    <input type="hidden" name="id" value="<?php echo $transaction['id']; ?>">
                <?php endif; ?>

                <div class="form-body">
                    <div class="form-grid">
                        <!-- Entry Type -->
                        <div class="form-group">
                            <label class="form-label" for="entry_type">Entry Type</label>
                            <select class="form-select" id="entry_type" name="entry_type">
                                <option value="">Select Entry Type</option>
                                <option value="credit" <?php echo ($edit_mode && $transaction['entry_type'] === 'credit') ? 'selected' : ''; ?>>Credit</option>
                                <option value="cash" <?php echo ($edit_mode && $transaction['entry_type'] === 'cash') ? 'selected' : ''; ?>>Cash</option>
                                <option value="to_pay" <?php echo ($edit_mode && $transaction['entry_type'] === 'to_pay') ? 'selected' : ''; ?>>To Pay</option>
                            </select>
                        </div>

                        <!-- Customer -->
                        <div class="form-group">
                            <label class="form-label" for="customer">Customer</label>
                            <input type="text" class="form-input" id="customer" name="customer"
                                   value="<?php echo $edit_mode ? htmlspecialchars($transaction['customer']) : ''; ?>">
                        </div>

                        <!-- Docket Number -->
                        <div class="form-group">
                            <label class="form-label" for="docket_no">Docket Number</label>
                            <input type="text" class="form-input" id="docket_no" name="docket_no"
                                   value="<?php echo $edit_mode ? htmlspecialchars($transaction['docket_no']) : ''; ?>">
                        </div>

                        <!-- Docket Date -->
                        <div class="form-group">
                            <label class="form-label" for="docket_date">Docket Date</label>
                            <input type="date" class="form-input" id="docket_date" name="docket_date"
                                   value="<?php echo $edit_mode ? $transaction['docket_date'] : date('Y-m-d'); ?>">
                        </div>

                        <!-- Pincode -->
                        <div class="form-group">
                            <label class="form-label" for="pincode">Pincode</label>
                            <input type="text" class="form-input" id="pincode" name="pincode"
                                   value="<?php echo $edit_mode ? htmlspecialchars($transaction['pincode']) : ''; ?>">
                        </div>

                        <!-- Destination -->
                        <div class="form-group">
                            <label class="form-label" for="destination">Destination</label>
                            <input type="text" class="form-input" id="destination" name="destination"
                                   value="<?php echo $edit_mode ? htmlspecialchars($transaction['destination']) : ''; ?>">
                        </div>

                        <!-- Weight -->
                        <div class="form-group">
                            <label class="form-label" for="weight">Weight (kg)</label>
                            <input type="number" step="0.01" class="form-input" id="weight" name="weight"
                                   value="<?php echo $edit_mode ? $transaction['weight'] : ''; ?>">
                        </div>

                        <!-- Mode of Transport -->
                        <div class="form-group">
                            <label class="form-label" for="mode_of_tsp">Mode of Transport</label>
                            <select class="form-select" id="mode_of_tsp" name="mode_of_tsp">
                                <option value="">Select Mode</option>
                                <option value="Express" <?php echo ($edit_mode && $transaction['mode_of_tsp'] === 'Express') ? 'selected' : ''; ?>>Express</option>
                                <option value="Surface" <?php echo ($edit_mode && $transaction['mode_of_tsp'] === 'Surface') ? 'selected' : ''; ?>>Surface</option>
                                <option value="Air Cargo" <?php echo ($edit_mode && $transaction['mode_of_tsp'] === 'Air Cargo') ? 'selected' : ''; ?>>Air Cargo</option>
                                <option value="Premium" <?php echo ($edit_mode && $transaction['mode_of_tsp'] === 'Premium') ? 'selected' : ''; ?>>Premium</option>
                                <option value="PTP" <?php echo ($edit_mode && $transaction['mode_of_tsp'] === 'PTP') ? 'selected' : ''; ?>>PTP</option>
                                <option value="COD" <?php echo ($edit_mode && $transaction['mode_of_tsp'] === 'COD') ? 'selected' : ''; ?>>COD</option>
                                <option value="International" <?php echo ($edit_mode && $transaction['mode_of_tsp'] === 'International') ? 'selected' : ''; ?>>International</option>
                                <option value="E-Com Express" <?php echo ($edit_mode && $transaction['mode_of_tsp'] === 'E-Com Express') ? 'selected' : ''; ?>>E-Com Express</option>
                                <option value="E-Com Surface" <?php echo ($edit_mode && $transaction['mode_of_tsp'] === 'E-Com Surface') ? 'selected' : ''; ?>>E-Com Surface</option>
                            </select>
                        </div>

                        <!-- Waybill Value -->
                        <div class="form-group">
                            <label class="form-label" for="waybill_value">Waybill Value (₹)</label>
                            <input type="number" step="0.01" class="form-input" id="waybill_value" name="waybill_value"
                                   value="<?php echo $edit_mode ? $transaction['waybill_value'] : ''; ?>">
                        </div>

                        <!-- Amount -->
                        <div class="form-group">
                            <label class="form-label" for="amount">Amount (₹)</label>
                            <input type="number" step="0.01" class="form-input" id="amount" name="amount"
                                   value="<?php echo $edit_mode ? $transaction['amount'] : ''; ?>">
                        </div>

                        <!-- Entry TS -->
                        <div class="form-group">
                            <label class="form-label" for="entry_ts">Entry TS Fee (₹)</label>
                            <input type="number" step="0.01" class="form-input" id="entry_ts" name="entry_ts"
                                   value="<?php echo $edit_mode ? $transaction['entry_ts'] : ''; ?>">
                        </div>

                        <!-- Waybill Percent -->
                        <div class="form-group">
                            <label class="form-label" for="waybill_percent">Waybill Percent (%)</label>
                            <input type="number" step="0.01" class="form-input" id="waybill_percent" name="waybill_percent"
                                   value="<?php echo $edit_mode ? $transaction['waybill_percent'] : ''; ?>">
                        </div>

                        <!-- ODA Charge -->
                        <div class="form-group">
                            <label class="form-label" for="oda_chrg">ODA Charge (₹)</label>
                            <input type="number" step="0.01" class="form-input" id="oda_chrg" name="oda_chrg"
                                   value="<?php echo $edit_mode ? $transaction['oda_chrg'] : ''; ?>">
                        </div>

                        <!-- Owner Risk -->
                        <div class="form-group">
                            <label class="form-label" for="owner_risk">Owner Risk (₹)</label>
                            <input type="number" step="0.01" class="form-input" id="owner_risk" name="owner_risk"
                                   value="<?php echo $edit_mode ? $transaction['owner_risk'] : ''; ?>">
                        </div>

                        <!-- Carrier Risk -->
                        <div class="form-group">
                            <label class="form-label" for="carrier_risk">Carrier Risk (₹)</label>
                            <input type="number" step="0.01" class="form-input" id="carrier_risk" name="carrier_risk"
                                   value="<?php echo $edit_mode ? $transaction['carrier_risk'] : ''; ?>">
                        </div>

                        <!-- Payment Status -->
                        <div class="form-group">
                            <label class="form-label" for="payment_status">Payment Status</label>
                            <select class="form-select" id="payment_status" name="payment_status">
                                <option value="Pending" <?php echo ($edit_mode && $transaction['payment_status'] === 'Pending') ? 'selected' : ''; ?>>Pending</option>
                                <option value="Cash-Received" <?php echo ($edit_mode && $transaction['payment_status'] === 'Cash-Received') ? 'selected' : ''; ?>>Cash Received</option>
                                <option value="Online-Received" <?php echo ($edit_mode && $transaction['payment_status'] === 'Online-Received') ? 'selected' : ''; ?>>Online Received</option>
                            </select>
                        </div>

                        <!-- Payment Received Date -->
                        <div class="form-group">
                            <label class="form-label" for="payment_received_date">Payment Received Date</label>
                            <input type="date" class="form-input" id="payment_received_date" name="payment_received_date"
                                   value="<?php echo $edit_mode ? $transaction['payment_received_date'] : ''; ?>">
                        </div>

                        <!-- Waybill -->
                        <div class="form-group">
                            <label class="form-label" for="waybill">Waybill</label>
                            <input type="text" class="form-input" id="waybill" name="waybill"
                                   value="<?php echo $edit_mode ? htmlspecialchars($transaction['waybill']) : ''; ?>">
                        </div>

                        <!-- Mobile 1 -->
                        <div class="form-group">
                            <label class="form-label" for="mobile1">Mobile 1</label>
                            <input type="tel" class="form-input" id="mobile1" name="mobile1"
                                   value="<?php echo $edit_mode ? htmlspecialchars($transaction['mobile1']) : ''; ?>">
                        </div>

                        <!-- Mobile 2 -->
                        <div class="form-group">
                            <label class="form-label" for="mobile2">Mobile 2</label>
                            <input type="tel" class="form-input" id="mobile2" name="mobile2"
                                   value="<?php echo $edit_mode ? htmlspecialchars($transaction['mobile2']) : ''; ?>">
                        </div>

                        <!-- Remarks -->
                        <div class="form-group full-width">
                            <label class="form-label" for="remarks">Remarks</label>
                            <textarea class="form-textarea" id="remarks" name="remarks"
                                      placeholder="Enter any additional remarks or notes..."><?php echo $edit_mode ? htmlspecialchars($transaction['remarks']) : ''; ?></textarea>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <a href="index.php?page=report.php" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        <?php echo $edit_mode ? 'Update' : 'Create'; ?> Transaction
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-hide success/error messages after 5 seconds
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.opacity = '0';
                    alert.style.transform = 'translateY(-10px)';
                    setTimeout(() => {
                        alert.remove();
                    }, 300);
                }, 5000);
            });

            // Form enhancement (validation removed as requested)

            // Payment status change handler
            const paymentStatus = document.getElementById('payment_status');
            const paymentDate = document.getElementById('payment_received_date');

            paymentStatus.addEventListener('change', function() {
                if (this.value === 'Cash-Received' || this.value === 'Online-Received') {
                    if (!paymentDate.value) {
                        paymentDate.value = new Date().toISOString().split('T')[0];
                    }
                } else if (this.value === 'Pending') {
                    paymentDate.value = '';
                }
            });
        });
    </script>
