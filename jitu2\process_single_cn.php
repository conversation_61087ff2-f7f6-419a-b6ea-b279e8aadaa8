<?php 
session_start();

// Ensure user is logged in
if (!isset($_SESSION['user_id']) || !isset($_SESSION['username'])) {
    die("<script>alert('Error: You must be logged in to create CN entries.'); window.location.href='login.php';</script>");
}

// Database Connection for InfinityFree
$servername = "localhost";
$username = "u111133901_yashent";
$password = "Yash@400709";
$database = "u111133901_yashent";
// Connect to MySQL
$conn = new mysqli($servername, $username, $password, $database);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Get form data
$cn_number = isset($_POST['cn_number']) ? trim($_POST['cn_number']) : "";
$cn_date = isset($_POST['cn_date']) ? $_POST['cn_date'] : "";
$cn_type = isset($_POST['cn_type']) ? $_POST['cn_type'] : "";
$logged_in_username = $_SESSION['username'];

// Validate CN Number
if (empty($cn_number)) {
    die("<script>alert('Error: CN Number is required.'); window.history.back();</script>");
}

// Prepare SQL Statement to Insert Data
$stmt = $conn->prepare("INSERT INTO cn_entries (cn_number, cn_date, cn_expiry_date, cn_type, username) VALUES (?, ?, ?, ?, ?)");
$cn_expiry_date = date('Y-m-d', strtotime($cn_date . ' + 180 days'));
$stmt->bind_param("sssss", $cn_number, $cn_date, $cn_expiry_date, $cn_type, $logged_in_username);

if ($stmt->execute()) {
    echo "<script>alert('✅ CN Entry successfully saved!'); window.location.href = 'https://yashenterprisesjitu.in/dashboard.php?page=cn-entry';</script>";
} else {
    echo "<script>alert('Error: Failed to save CN Entry.'); window.history.back();</script>";
}

// Close the statement & connection
$stmt->close();
$conn->close();
exit();
?> 