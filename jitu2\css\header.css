.navbar {
    background-color: #87CEEB;
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    box-sizing: border-box;
}

.navbar-brand {
    font-size: 24px;
    font-weight: bold;
    padding-right: 20px;
}

.navbar-nav {
    list-style: none;
    display: flex;
    gap: 25px;
    margin: 0;
    padding: 0;
}

.navbar-nav a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: opacity 0.3s;
}

.navbar-nav a:hover {
    opacity: 0.8;
}

/* User Profile Styles */
.user-profile {
    position: relative;
    display: flex;
    align-items: center;
}

.user-icon {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px 15px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.user-icon:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.user-icon i {
    font-size: 20px;
    margin-right: 8px;
}

.user-icon span {
    font-size: 14px;
    font-weight: 500;
}

.profile-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: none;
    min-width: 200px;
    margin-top: 5px;
}

.profile-dropdown a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #333;
    text-decoration: none;
    transition: background-color 0.3s;
}

.profile-dropdown a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
    color: #87CEEB;
}

.profile-dropdown a:hover {
    background-color: #e0f7fa;
}

.user-profile:hover .profile-dropdown {
    display: block;
} 