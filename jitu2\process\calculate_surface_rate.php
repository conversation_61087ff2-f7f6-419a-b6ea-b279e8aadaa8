<?php
function calculateSurfaceRate($weight, $rate_1_10, $rate_10_20, $rate_above_20, $min_weight) {
    // If weight is less than minimum chargeable weight, use minimum weight
    $actual_weight = max($weight, $min_weight);
    
    // Calculate in rounds of 1 kg
    $rounded_weight = ceil($actual_weight);
    
    // Calculate total rate based on weight range
    if ($rounded_weight <= 10) {
        // For weights up to 10 kg
        $total_rate = $rounded_weight * $rate_1_10;
    } elseif ($rounded_weight <= 20) {
        // For weights between 10-20 kg
        $total_rate = $rounded_weight * $rate_10_20;
    } else {
        // For weights above 20 kg
        $total_rate = $rounded_weight * $rate_above_20;
    }
    
    return [
        'actual_weight' => $actual_weight,
        'rounded_weight' => $rounded_weight,
        'total_rate' => $total_rate,
        'rate_used' => ($rounded_weight <= 10) ? $rate_1_10 : 
                      (($rounded_weight <= 20) ? $rate_10_20 : $rate_above_20)
    ];
}
?> 