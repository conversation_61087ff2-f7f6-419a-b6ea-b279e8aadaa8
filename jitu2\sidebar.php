<?php
$current_page = isset($_GET['page']) ? $_GET['page'] : 'dashboard';
?>

<!-- Modern Sidebar Component -->
<style>
    :root {
        /* Match webapp color scheme */
        --primary-blue: #2196F3;
        --light-blue: #E3F2FD;
        --hover-blue: #1976D2;
        --sky-blue: #87CEEB;
        --text-dark: #2c3e50;
        --border-color: #e0e0e0;
        --background: #F8FAFC;

        /* Sidebar specific colors */
        --sidebar-bg: #ffffff;
        --sidebar-shadow: rgba(33, 150, 243, 0.1);
        --text-primary: #2c3e50;
        --text-secondary: #64748b;
        --text-muted: #94a3b8;
        --sidebar-width: 280px;
        --header-height: 70px;

        /* Gradients matching webapp theme */
        --gradient-primary: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
        --gradient-accent: linear-gradient(135deg, #87CEEB 0%, #2196F3 100%);
        --gradient-performance: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
        --gradient-reports: linear-gradient(135deg, #87CEEB 0%, #2196F3 100%);
        --gradient-management: linear-gradient(135deg, #E3F2FD 0%, #2196F3 100%);
    }

    .sidebar {
        width: var(--sidebar-width);
        height: calc(100vh - var(--header-height));
        background: var(--sidebar-bg);
        position: fixed;
        left: 0;
        top: var(--header-height);
        z-index: 999;
        overflow-y: auto;
        overflow-x: hidden;
        box-shadow: 4px 0 20px var(--sidebar-shadow);
        transition: all 0.3s ease;
        border-right: 1px solid var(--border-color);
    }

    .sidebar::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(180deg, var(--background) 0%, #ffffff 100%);
        z-index: -1;
    }

    .sidebar::-webkit-scrollbar {
        width: 6px;
    }

    .sidebar::-webkit-scrollbar-track {
        background: var(--background);
        border-radius: 3px;
    }

    .sidebar::-webkit-scrollbar-thumb {
        background: var(--primary-blue);
        border-radius: 3px;
        opacity: 0.7;
    }

    .sidebar::-webkit-scrollbar-thumb:hover {
        background: var(--hover-blue);
        opacity: 1;
    }

    /* Navigation */
    .sidebar-nav {
        padding: 1rem 0 2rem;
    }

    .menu-section {
        margin-bottom: 2rem;
        position: relative;
    }

    .menu-section::before {
        content: '';
        position: absolute;
        top: -0.5rem;
        left: 1.5rem;
        right: 1.5rem;
        height: 1px;
        background: linear-gradient(90deg, transparent, var(--border-color), transparent);
    }

    .menu-section:first-child::before {
        display: none;
    }

    .menu-section-title {
        color: var(--text-secondary);
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.1em;
        padding: 1rem 1.5rem 0.5rem;
        position: relative;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .menu-section-title i {
        font-size: 0.8rem;
        color: var(--primary-blue);
    }

    .menu-section-title::after {
        content: '';
        flex: 1;
        height: 1px;
        background: linear-gradient(90deg, var(--primary-blue), transparent);
        margin-left: 0.75rem;
        border-radius: 1px;
    }

    .menu {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .menu li {
        margin: 0.125rem 0;
    }

    .menu a {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 1.5rem;
        color: var(--text-secondary);
        text-decoration: none;
        font-weight: 500;
        font-size: 0.875rem;
        transition: all 0.3s ease;
        position: relative;
        margin: 0 0.5rem;
        border-radius: 8px;
        border: 1px solid transparent;
    }

    .menu a::before {
        content: '';
        position: absolute;
        left: -0.5rem;
        top: 50%;
        transform: translateY(-50%) scaleY(0);
        width: 3px;
        height: 60%;
        background: var(--primary-blue);
        border-radius: 0 3px 3px 0;
        transition: all 0.3s ease;
    }

    .menu a:hover {
        background: var(--light-blue);
        color: var(--text-primary);
        transform: translateX(4px);
        border-color: var(--border-color);
        box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1);
    }

    .menu a:hover::before {
        transform: translateY(-50%) scaleY(1);
    }

    .menu a.active {
        background: var(--light-blue);
        color: var(--primary-blue);
        font-weight: 600;
        border-color: var(--primary-blue);
        box-shadow: 0 2px 12px rgba(33, 150, 243, 0.2);
    }

    .menu a.active::before {
        transform: translateY(-50%) scaleY(1);
        background: var(--primary-blue);
    }

    .menu-icon {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.1rem;
        transition: all 0.3s ease;
    }

    .menu a:hover .menu-icon {
        transform: scale(1.1);
    }

    .menu-text {
        flex: 1;
        font-weight: 500;
    }

    .menu-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.5rem;
        border-radius: 6px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    /* Special Section Styles */
    .reports-section .menu-section-title {
        color: var(--sky-blue);
        font-weight: 700;
    }

    .management-section .menu-section-title {
        color: var(--hover-blue);
        font-weight: 700;
    }

    .badge-new {
        background: var(--primary-blue);
        color: white;
        animation: pulse-glow 2s infinite;
        box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
    }

    .badge-hot {
        background: var(--sky-blue);
        color: white;
    }

    .badge-updated {
        background: var(--hover-blue);
        color: white;
    }

    @keyframes pulse-glow {
        0%, 100% {
            opacity: 1;
            transform: scale(1);
        }
        50% {
            opacity: 0.8;
            transform: scale(1.05);
        }
    }

    @media (max-width: 768px) {
        .sidebar {
            transform: translateX(-100%);
            box-shadow: none;
            height: calc(100vh - var(--header-height));
            top: var(--header-height);
        }

        .sidebar.active {
            transform: translateX(0);
            box-shadow: 4px 0 20px rgba(33, 150, 243, 0.2);
        }

        .main-content {
            margin-left: 0 !important;
        }
    }
</style>

<aside class="sidebar">
    <!-- Navigation -->
    <nav class="sidebar-nav">
        <!-- Main Navigation -->
        <div class="menu-section">
            <div class="menu-section-title">
                <i class="fas fa-home"></i>
                Main Navigation
            </div>
            <ul class="menu">
                <li>
                    <a href="dashboard.php?page=dashboard" class="<?php echo ($current_page === 'dashboard' ? 'active' : ''); ?>">
                        <div class="menu-icon"><i class="fas fa-chart-line"></i></div>
                        <div class="menu-text">Dashboard</div>
                    </a>
                </li>
                <li>
                    <a href="dashboard.php?page=cash-entry" class="<?php echo ($current_page === 'cash-entry' ? 'active' : ''); ?>">
                        <div class="menu-icon"><i class="fas fa-money-bill-wave"></i></div>
                        <div class="menu-text">Cash Booking Entry</div>
                    </a>
                </li>
                <li>
                    <a href="dashboard.php?page=credit-entry" class="<?php echo ($current_page === 'credit-entry' ? 'active' : ''); ?>">
                        <div class="menu-icon"><i class="fas fa-credit-card"></i></div>
                        <div class="menu-text">Credit Booking Entry</div>
                    </a>
                </li>
                <li>
                    <a href="dashboard.php?page=pending_cash" class="<?php echo ($current_page === 'pending_cash' ? 'active' : ''); ?>">
                        <div class="menu-icon"><i class="fas fa-clock"></i></div>
                        <div class="menu-text">Pending Cash Records</div>
                    </a>
                </li>
                <li>
                    <a href="dashboard.php?page=cash_report" class="<?php echo ($current_page === 'cash_report' ? 'active' : ''); ?>">
                        <div class="menu-icon"><i class="fas fa-file-invoice-dollar"></i></div>
                        <div class="menu-text">Cash Report</div>
                    </a>
                </li>
                <li>
                    <a href="dashboard.php?page=upload" class="<?php echo ($current_page === 'upload' ? 'active' : ''); ?>">
                        <div class="menu-icon"><i class="fas fa-upload"></i></div>
                        <div class="menu-text">Upload</div>
                    </a>
                </li>
            </ul>
        </div>

        <!-- Reports & Analysis -->
        <div class="menu-section reports-section">
            <div class="menu-section-title">
                <i class="fas fa-chart-bar"></i>
                Reports & Analysis
            </div>
            <ul class="menu">
                <li>
                    <a href="dashboard.php?page=report" class="<?php echo ($current_page === 'report' ? 'active' : ''); ?>">
                        <div class="menu-icon"><i class="fas fa-file-alt"></i></div>
                        <div class="menu-text">Cash & Credit Report</div>
                    </a>
                </li>
                <li>
                    <a href="dashboard.php?page=cn-entry" class="<?php echo ($current_page === 'cn-entry' ? 'active' : ''); ?>">
                        <div class="menu-icon"><i class="fas fa-file-invoice"></i></div>
                        <div class="menu-text">C-Note Entry</div>
                    </a>
                </li>
                <li>
                    <a href="dashboard.php?page=available_cn_report" class="<?php echo ($current_page === 'available_cn_report' ? 'active' : ''); ?>">
                        <div class="menu-icon"><i class="fas fa-clipboard-check"></i></div>
                        <div class="menu-text">C-Note Report</div>
                    </a>
                </li>
                <li>
                    <a href="dashboard.php?page=freight_rate-calculator" class="<?php echo ($current_page === 'freight_rate-calculator' ? 'active' : ''); ?>">
                        <div class="menu-icon"><i class="fas fa-calculator"></i></div>
                        <div class="menu-text">Freight Rate Calculator</div>
                        <div class="menu-badge badge-hot">HOT</div>
                    </a>
                </li>
                <li>
                    <a href="dashboard.php?page=booking_data" class="<?php echo ($current_page === 'booking_data' ? 'active' : ''); ?>">
                        <div class="menu-icon"><i class="fas fa-database"></i></div>
                        <div class="menu-text">Missing Data Analysis</div>
                    </a>
                </li>
                <li>
                    <a href="dashboard.php?page=tracking_report" class="<?php echo ($current_page === 'tracking_report' ? 'active' : ''); ?>">
                        <div class="menu-icon"><i class="fas fa-route"></i></div>
                        <div class="menu-text">Daily Tracking Report</div>
                    </a>
                </li>
            </ul>
        </div>

        <!-- Management -->
        <div class="menu-section management-section">
            <div class="menu-section-title">
                <i class="fas fa-cogs"></i>
                Management
            </div>
            <ul class="menu">
                <li>
                    <a href="dashboard.php?page=customer_dashboard" class="<?php echo ($current_page === 'customer_dashboard' ? 'active' : ''); ?>">
                        <div class="menu-icon"><i class="fas fa-users"></i></div>
                        <div class="menu-text">Manage Customers</div>
                    </a>
                </li>
                <li>
                    <a href="dashboard.php?page=inv_ts_report" class="<?php echo ($current_page === 'inv_ts_report' ? 'active' : ''); ?>">
                        <div class="menu-icon"><i class="fas fa-file-invoice-dollar"></i></div>
                        <div class="menu-text">Bill vs TS Report</div>
                    </a>
                </li>
                <li>
                    <a href="dashboard.php?page=outstanding" class="<?php echo ($current_page === 'outstanding' ? 'active' : ''); ?>">
                        <div class="menu-icon"><i class="fas fa-hand-holding-usd"></i></div>
                        <div class="menu-text">Billing & Payment Report</div>
                    </a>
                </li>
                <li>
                    <a href="dashboard.php?page=rate_master" class="<?php echo ($current_page === 'rate_master' ? 'active' : ''); ?>">
                        <div class="menu-icon"><i class="fas fa-tags"></i></div>
                        <div class="menu-text">Customer Pricing</div>
                    </a>
                </li>
                <li>
                    <a href="dashboard.php?page=ts_rate_master" class="<?php echo ($current_page === 'ts_rate_master' ? 'active' : ''); ?>">
                        <div class="menu-icon"><i class="fas fa-tag"></i></div>
                        <div class="menu-text">Service Provider Rates</div>
                    </a>
                </li>
                <li>
                    <a href="dashboard.php?page=invoice_dashboard" class="<?php echo ($current_page === 'invoice_dashboard' ? 'active' : ''); ?>">
                        <div class="menu-icon"><i class="fas fa-file-invoice"></i></div>
                        <div class="menu-text">Invoice Management</div>
                    </a>
                </li>
            </ul>
        </div>

        <!-- Data Tools -->
        <div class="menu-section">
            <div class="menu-section-title">
                <i class="fas fa-database"></i>
                Data Tools
            </div>
            <ul class="menu">
                <li>
                    <a href="dashboard.php?page=transaction_form" class="<?php echo ($current_page === 'transaction_form' ? 'active' : ''); ?>">
                        <div class="menu-icon"><i class="fas fa-edit"></i></div>
                        <div class="menu-text">Direct Manual Entry</div>
                    </a>
                </li>
                <li>
                    <a href="dashboard.php?page=transactions_view" class="<?php echo ($current_page === 'transactions_view' ? 'active' : ''); ?>">
                        <div class="menu-icon"><i class="fas fa-table"></i></div>
                        <div class="menu-text">Transactions Viewer</div>
                        <div class="menu-badge badge-updated">UPDATED</div>
                    </a>
                </li>
                <li>
                    <a href="dashboard.php?page=pincode" class="<?php echo ($current_page === 'pincode' ? 'active' : ''); ?>">
                        <div class="menu-icon"><i class="fas fa-map-marker-alt"></i></div>
                        <div class="menu-text">Pincode Management</div>
                    </a>
                </li>
            </ul>
        </div>

        <!-- User Tools -->
        <div class="menu-section">
            <div class="menu-section-title">
                <i class="fas fa-user-cog"></i>
                User Tools
            </div>
            <ul class="menu">
                <li>
                    <a href="dashboard.php?page=profile" class="<?php echo ($current_page === 'profile' ? 'active' : ''); ?>">
                        <div class="menu-icon"><i class="fas fa-user"></i></div>
                        <div class="menu-text">User Profile</div>
                    </a>
                </li>
                <li>
                    <a href="dashboard.php?page=settings" class="<?php echo ($current_page === 'settings' ? 'active' : ''); ?>">
                        <div class="menu-icon"><i class="fas fa-cog"></i></div>
                        <div class="menu-text">Settings</div>
                    </a>
                </li>
            </ul>
        </div>
    </nav>
</aside>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const sidebar = document.querySelector('.sidebar');
        const menuItems = document.querySelectorAll('.menu a');
        const menuSections = document.querySelectorAll('.menu-section');

        // Enhanced hover effects
        menuItems.forEach(item => {
            item.addEventListener('mouseenter', function() {
                const icon = this.querySelector('.menu-icon');
                if (icon) {
                    icon.style.transform = 'scale(1.1) rotate(5deg)';
                }
            });

            item.addEventListener('mouseleave', function() {
                const icon = this.querySelector('.menu-icon');
                if (icon) {
                    icon.style.transform = 'scale(1) rotate(0deg)';
                }
            });
        });

        // Add click ripple effect
        menuItems.forEach(item => {
            item.addEventListener('click', function(e) {
                const ripple = document.createElement('span');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                ripple.classList.add('ripple');

                this.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });

        // Badge animation for NEW items
        const badges = document.querySelectorAll('.badge-new');
        badges.forEach(badge => {
            setInterval(() => {
                badge.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    badge.style.transform = 'scale(1)';
                }, 200);
            }, 3000);
        });

        // Handle window resize
        let resizeTimer;
        window.addEventListener('resize', function() {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(function() {
                if (window.innerWidth > 768) {
                    sidebar.classList.remove('active');
                }
            }, 250);
        });
    });

    // Add enhanced styles
    const style = document.createElement('style');
    style.textContent = `
        .menu a {
            position: relative;
            overflow: hidden;
        }

        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        .menu-icon {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .menu-badge {
            transition: all 0.2s ease;
        }
    `;
    document.head.appendChild(style);
</script>
