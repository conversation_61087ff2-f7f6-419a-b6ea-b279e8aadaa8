<?php
session_start();
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    echo json_encode(['success' => false, 'error' => 'Not logged in']);
    exit;
}

// Include database connection
include '../db_connect.php';

$type = $_GET['type'] ?? '';
$username = $_SESSION['username'];

try {
    if ($type === 'cash') {
        // For cash-entry.php - fetch CN numbers and settings
        $query = "SELECT 
            'cn_number' as data_type, cn.cn_number as value, NULL as region_series, 
            NULL as surface_docket, NULL as aircargo_docket, NULL as premium_docket, 
            NULL as ptp_docket, NULL as cod_docket, NULL as international_docket, 
            NULL as ecom_express_docket, NULL as ecom_surface_docket
        FROM cn_entries cn 
        LEFT JOIN transactions t ON cn.cn_number = t.docket_no 
        WHERE t.docket_no IS NULL AND cn.username = ?

        UNION ALL

        SELECT 
            'settings' as data_type, NULL as value, s.region_series, 
            s.surface_docket, s.aircargo_docket, s.premium_docket, 
            s.ptp_docket, s.cod_docket, s.international_docket, 
            s.ecom_express_docket, s.ecom_surface_docket
        FROM settings s 
        WHERE s.username = ?

        ORDER BY data_type, value ASC";

        $stmt = $conn->prepare($query);
        $stmt->bind_param("ss", $username, $username);
        
    } elseif ($type === 'credit') {
        // For credit-entry.php - fetch CN numbers, customers, and settings
        $query = "SELECT 
            'cn_number' as data_type, cn.cn_number as value, NULL as region_series, 
            NULL as surface_docket, NULL as aircargo_docket, NULL as premium_docket, 
            NULL as ptp_docket, NULL as cod_docket, NULL as international_docket, 
            NULL as ecom_express_docket, NULL as ecom_surface_docket
        FROM cn_entries cn 
        LEFT JOIN transactions t ON cn.cn_number = t.docket_no 
        WHERE t.docket_no IS NULL AND cn.username = ?

        UNION ALL

        SELECT 
            'customer' as data_type, c.short_name as value, NULL as region_series, 
            NULL as surface_docket, NULL as aircargo_docket, NULL as premium_docket, 
            NULL as ptp_docket, NULL as cod_docket, NULL as international_docket, 
            NULL as ecom_express_docket, NULL as ecom_surface_docket
        FROM customers c 
        WHERE c.username = ?

        UNION ALL

        SELECT 
            'settings' as data_type, NULL as value, s.region_series, 
            s.surface_docket, s.aircargo_docket, s.premium_docket, 
            s.ptp_docket, s.cod_docket, s.international_docket, 
            s.ecom_express_docket, s.ecom_surface_docket
        FROM settings s 
        WHERE s.username = ?

        ORDER BY data_type, value ASC";

        $stmt = $conn->prepare($query);
        $stmt->bind_param("sss", $username, $username, $username);
        
    } else {
        echo json_encode(['success' => false, 'error' => 'Invalid type parameter']);
        exit;
    }

    $stmt->execute();
    $result = $stmt->get_result();

    // Initialize response data
    $response = [
        'success' => true,
        'cn_numbers' => [],
        'customer_names' => [],
        'settings' => [
            'region_series' => '',
            'docket_initials' => [
                'surface' => '',
                'aircargo' => '',
                'premium' => '',
                'ptp' => '',
                'cod' => '',
                'international' => '',
                'ecom_express' => '',
                'ecom_surface' => ''
            ]
        ]
    ];

    // Process results
    while ($row = $result->fetch_assoc()) {
        if ($row['data_type'] === 'cn_number') {
            $response['cn_numbers'][] = $row['value'];
        } elseif ($row['data_type'] === 'customer') {
            $response['customer_names'][] = $row['value'];
        } elseif ($row['data_type'] === 'settings') {
            $response['settings']['region_series'] = $row['region_series'];
            $response['settings']['docket_initials'] = [
                'surface' => $row['surface_docket'],
                'aircargo' => $row['aircargo_docket'],
                'premium' => $row['premium_docket'],
                'ptp' => $row['ptp_docket'],
                'cod' => $row['cod_docket'],
                'international' => $row['international_docket'],
                'ecom_express' => $row['ecom_express_docket'],
                'ecom_surface' => $row['ecom_surface_docket']
            ];
        }
    }

    echo json_encode($response);

} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => 'Database error: ' . $e->getMessage()]);
}
?>
