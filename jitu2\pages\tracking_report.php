<?php
session_start();
include 'db_connect.php';

if (!isset($_SESSION['username'])) {
    header("Location: login.php");
    exit();
}

// Sync data from transactions to tracking table
$username = $_SESSION['username'];

// Get filter values
$current_month = date('m-Y');
$selected_month = $_GET['month'] ?? $current_month;
$selected_status = $_GET['status'] ?? '';
$page = isset($_GET['page_num']) ? max(1, intval($_GET['page_num'])) : 1;
$per_page = 50;

// Generate last 12 months for dropdown
$months = [];
for ($i = 0; $i < 12; $i++) {
    $date = strtotime("-$i months");
    $month_num = date('m', $date);
    $year = date('Y', $date);
    $month_name = date('M', $date);
    $months[] = [
        'value' => $month_num . '-' . $year,
        'label' => $month_name . '-' . $year
    ];
}

// First, get all transactions that need to be synced
$sync_query = "INSERT INTO tracking (username, tr_entry_type, tr_cust, tr_docket_no, tr_docket_dt, tr_mot, tr_dest)
               SELECT t.username, 
                      t.entry_type as tr_entry_type,
                      t.customer as tr_cust,
                      t.docket_no as tr_docket_no,
                      t.docket_date as tr_docket_dt,
                      t.mode_of_tsp as tr_mot,
                      t.destination as tr_dest
               FROM transactions t
               LEFT JOIN tracking tr ON t.docket_no = tr.tr_docket_no AND t.username = tr.username
               WHERE t.username = ? AND tr.tr_docket_no IS NULL";

try {
    $stmt = $conn->prepare($sync_query);
    $stmt->bind_param("s", $username);
    $stmt->execute();
    
    // Update existing records
    $update_query = "UPDATE tracking tr
                    INNER JOIN transactions t ON t.docket_no = tr.tr_docket_no AND t.username = tr.username
                    SET tr.tr_entry_type = t.entry_type,
                        tr.tr_cust = t.customer,
                        tr.tr_docket_dt = t.docket_date,
                        tr.tr_mot = t.mode_of_tsp,
                        tr.tr_dest = t.destination
                    WHERE tr.username = ?";
                    
    $stmt = $conn->prepare($update_query);
    $stmt->bind_param("s", $username);
    $stmt->execute();
} catch (Exception $e) {
    // Log error if needed
    error_log("Sync error: " . $e->getMessage());
}

// Handle AJAX update
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_id'])) {
    // Clear any previous output
    ob_clean();
    
    // Set JSON header
    header('Content-Type: application/json');
    
    try {
        if (!isset($_SESSION['username'])) {
            throw new Exception('Session expired');
        }
        
        $id = $_POST['update_id'];
        $comment = $_POST['comment'];
        $status = $_POST['status'];
        $username = $_SESSION['username'];
        
        $stmt = $conn->prepare("UPDATE tracking SET tr_comment = ?, tr_opcl = ? WHERE id = ? AND username = ?");
        $stmt->bind_param("ssis", $comment, $status, $id, $username);
        
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Record updated successfully!']);
        } else {
            throw new Exception('Update failed: ' . $stmt->error);
        }
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit();
}

// Handle AJAX search
if (isset($_GET['ajax_search'])) {
    try {
        $username = $_SESSION['username'];
        $search_query = $_GET['search'] ?? '';
        $selected_month = $_GET['month'] ?? date('m-Y');
        $selected_status = $_GET['status'] ?? '';
        $page = isset($_GET['page_num']) ? max(1, intval($_GET['page_num'])) : 1;
        $per_page = 50;
        
        $data = [];
        $month_year = explode('-', $selected_month);
        
        // First get total count for pagination
        if (!empty($search_query)) {
            $count_sql = "SELECT COUNT(*) as total FROM tracking 
                         WHERE username = ? 
                         AND tr_docket_no LIKE ?
                         AND MONTH(tr_docket_dt) = ? 
                         AND YEAR(tr_docket_dt) = ?";
            $params = [$username, "%{$search_query}%", $month_year[0], $month_year[1]];
            $types = "ssii";
            
            if (!empty($selected_status)) {
                $count_sql .= " AND tr_opcl = ?";
                $params[] = $selected_status;
                $types .= "s";
            }
            
            $count_stmt = $conn->prepare($count_sql);
            $count_stmt->bind_param($types, ...$params);
        } else {
            $count_sql = "SELECT COUNT(*) as total FROM tracking 
                         WHERE username = ? 
                         AND MONTH(tr_docket_dt) = ? 
                         AND YEAR(tr_docket_dt) = ?";
            $params = [$username, $month_year[0], $month_year[1]];
            $types = "sii";
            
            if (!empty($selected_status)) {
                $count_sql .= " AND tr_opcl = ?";
                $params[] = $selected_status;
                $types .= "s";
            }
            
            $count_stmt = $conn->prepare($count_sql);
            $count_stmt->bind_param($types, ...$params);
        }
        
        $count_stmt->execute();
        $total_records = $count_stmt->get_result()->fetch_assoc()['total'];
        $total_pages = ceil($total_records / $per_page);
        $offset = ($page - 1) * $per_page;
        
        // Now get the actual data with pagination
        if (!empty($search_query)) {
            $sql = "SELECT * FROM tracking 
                    WHERE username = ? 
                    AND tr_docket_no LIKE ?
                    AND MONTH(tr_docket_dt) = ? 
                    AND YEAR(tr_docket_dt) = ?";
            $params = [$username, "%{$search_query}%", $month_year[0], $month_year[1]];
            $types = "ssii";
            
            if (!empty($selected_status)) {
                $sql .= " AND tr_opcl = ?";
                $params[] = $selected_status;
                $types .= "s";
            }
            
            $sql .= " ORDER BY tr_docket_dt DESC LIMIT ? OFFSET ?";
            $params[] = $per_page;
            $params[] = $offset;
            $types .= "ii";
            
            $stmt = $conn->prepare($sql);
            $stmt->bind_param($types, ...$params);
        } else {
            $sql = "SELECT * FROM tracking 
                    WHERE username = ? 
                    AND MONTH(tr_docket_dt) = ? 
                    AND YEAR(tr_docket_dt) = ?";
            $params = [$username, $month_year[0], $month_year[1]];
            $types = "sii";
            
            if (!empty($selected_status)) {
                $sql .= " AND tr_opcl = ?";
                $params[] = $selected_status;
                $types .= "s";
            }
            
            $sql .= " ORDER BY tr_docket_dt DESC LIMIT ? OFFSET ?";
            $params[] = $per_page;
            $params[] = $offset;
            $types .= "ii";
            
            $stmt = $conn->prepare($sql);
            $stmt->bind_param($types, ...$params);
        }
        
        if (!$stmt->execute()) {
            throw new Exception("Database error: " . $stmt->error);
        }
        
        $result = $stmt->get_result();
        
        while ($row = $result->fetch_assoc()) {
            $data[] = [
                'id' => $row['id'],
                'tr_entry_type' => htmlspecialchars($row['tr_entry_type']),
                'tr_cust' => htmlspecialchars($row['tr_cust']),
                'tr_docket_no' => htmlspecialchars($row['tr_docket_no']),
                'tr_docket_dt' => date('d M Y', strtotime($row['tr_docket_dt'])),
                'tr_mot' => htmlspecialchars($row['tr_mot']),
                'tr_dest' => htmlspecialchars($row['tr_dest']),
                'tr_edd' => $row['tr_edd'] ? date('d M Y', strtotime($row['tr_edd'])) : '',
                'tr_delsts' => htmlspecialchars($row['tr_delsts']),
                'tr_lststs_dt' => $row['tr_lststs_dt'] ? date('d M Y', strtotime($row['tr_lststs_dt'])) : '',
                'tr_comment' => htmlspecialchars($row['tr_comment']),
                'tr_opcl' => htmlspecialchars($row['tr_opcl'])
            ];
        }
        
        ob_clean();
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'data' => $data,
            'total' => $total_records,
            'current_page' => $page,
            'total_pages' => $total_pages
        ]);
        
    } catch (Exception $e) {
        ob_clean();
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    } finally {
        if (isset($stmt)) $stmt->close();
        if (isset($count_stmt)) $count_stmt->close();
        if (isset($conn)) $conn->close();
    }
    exit;
}

// Initial data load with pagination and month filter
$username = $_SESSION['username'];
$month_year = explode('-', $selected_month);
$offset = ($page - 1) * $per_page;

// Get total count first
$count_sql = "SELECT COUNT(*) as total FROM tracking 
              WHERE username = ? 
              AND MONTH(tr_docket_dt) = ? 
              AND YEAR(tr_docket_dt) = ?";
$params = [$username, $month_year[0], $month_year[1]];
$types = "sii";

if (!empty($selected_status)) {
    $count_sql .= " AND tr_opcl = ?";
    $params[] = $selected_status;
    $types .= "s";
}

$count_stmt = $conn->prepare($count_sql);
$count_stmt->bind_param($types, ...$params);
$count_stmt->execute();
$total_records = $count_stmt->get_result()->fetch_assoc()['total'];
$total_pages = ceil($total_records / $per_page);

// Get paginated data
$sql = "SELECT * FROM tracking 
        WHERE username = ? 
        AND MONTH(tr_docket_dt) = ? 
        AND YEAR(tr_docket_dt) = ?";
$params = [$username, $month_year[0], $month_year[1]];
$types = "sii";

if (!empty($selected_status)) {
    $sql .= " AND tr_opcl = ?";
    $params[] = $selected_status;
    $types .= "s";
}

$sql .= " ORDER BY tr_docket_dt DESC LIMIT ? OFFSET ?";
$params[] = $per_page;
$params[] = $offset;
$types .= "ii";

$stmt = $conn->prepare($sql);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$result = $stmt->get_result();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Inline Critical CSS -->
    <style>
        /* Critical styles that should load first */
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            overflow: hidden;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #2196F3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        #mainContent {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease-in;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .report-container {
            opacity: 0;
            transition: opacity 0.3s ease-in;
        }

        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 4px;
            background-color: #4CAF50;
            color: white;
            font-size: 16px;
            z-index: 1000;
            display: none;
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .show-message {
            display: block;
        }

        .search-section {
            flex: 1;
            max-width: none;
            min-width: 280px;
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 1rem;
        }

        .search-container {
            position: relative;
            flex: 1;
            max-width: 500px;
        }

        .upload-section {
            width: auto;
        }

        .upload-form {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            width: auto;
        }

        .file-input {
            width: 300px;
            padding: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: white;
        }

        .btn-upload {
            padding: 0.5rem 1rem;
            border-radius: 4px;
            font-size: 0.9rem;
            text-decoration: none;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
            background: var(--light-blue);
            color: var(--primary-blue);
            border: 1px solid transparent;
            cursor: pointer;
        }

        .btn-upload:hover {
            background: var(--primary-blue);
            color: white;
        }

        /* Add new success alert styles */
        .success-alert {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            background-color: #4CAF50;
            color: white;
            border-radius: 4px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            display: none;
            animation: slideInRight 0.5s ease-out;
            font-size: 16px;
            font-weight: 500;
        }

        /* Filter Styles */
        .filters-section {
            width: 100%;
            margin-bottom: 0.75rem;
            background: white;
            padding: 1rem;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            display: block !important;
            visibility: visible !important;
        }

        .filters-grid {
            display: flex;
            gap: 1.5rem;
            align-items: flex-end;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .filter-group label {
            font-size: 0.85rem;
            color: var(--text-dark);
            font-weight: 500;
        }

        .filter-input {
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            background: var(--background);
            min-width: 180px;
        }

        .filter-input:focus {
            outline: none;
            border-color: var(--primary-blue);
            background: white;
            box-shadow: 0 0 0 4px rgba(33, 150, 243, 0.1);
        }

        .btn-reset {
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            text-align: center;
            background: var(--light-blue);
            color: var(--primary-blue);
            border: 1px solid transparent;
            white-space: nowrap;
        }

        .btn-reset:hover {
            background: var(--primary-blue);
            color: white;
        }

        /* Search Styles */
        .search-container {
            flex: 1;
            min-width: 300px;
        }

        .search-input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .search-box {
            width: 100%;
            padding: 0.75rem;
            padding-right: 70px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            background: var(--background);
        }

        .search-box:focus {
            outline: none;
            border-color: var(--primary-blue);
            background: white;
            box-shadow: 0 0 0 4px rgba(33, 150, 243, 0.1);
        }

        .clear-search {
            position: absolute;
            right: 8px;
            padding: 0.4rem 0.8rem;
            border: none;
            background: var(--light-blue);
            color: var(--primary-blue);
            border-radius: 4px;
            font-size: 0.85rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: none;
        }

        .clear-search:hover {
            background: var(--primary-blue);
            color: white;
        }

        .loading-indicator {
            position: absolute;
            right: -30px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            border: 2px solid var(--border-color);
            border-top-color: var(--primary-blue);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            display: none;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .success-alert.show {
            display: block;
        }

        .success-alert i {
            margin-right: 8px;
        }
    </style>

    <!-- Deferred non-critical CSS -->
    <style>
        :root {
            --primary-blue: #2196F3;
            --light-blue: #E3F2FD;
            --hover-blue: #1976D2;
            --sky-blue: #87CEEB;
            --text-dark: #2c3e50;
            --border-color: #e0e0e0;
            --background: #F8FAFC;
        }

        .report-container {
            padding: 2rem;
            margin: 0 auto;
            max-width: 2000px;
            position: relative;
            margin-left: 0px;
            margin-top: 0px;
        }

        .header-section {
            background: white;
            padding: 0.75rem 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            margin-bottom: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .header-section1 {
            background: white;
            padding: 0.75rem 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            margin-bottom: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .btn-download {
            padding: 0.5rem 1rem;
            border-radius: 4px;
            font-size: 0.9rem;
            text-decoration: none;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
            background: var(--light-blue);
            color: var(--primary-blue);
            border: 1px solid transparent;
            cursor: pointer;
        }

        .btn-download:hover {
            background: var(--primary-blue);
            color: white;
        }

        .page-title {
            color: var(--primary-blue);
            font-size: 1.75rem;
            font-weight: 600;
            margin: 0;
        }

        .search-container {
            position: relative;
            width: 100%;
        }

        .search-box {
            width: 100%;
            padding: 0.75rem 3.5rem 0.75rem 1rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: var(--background);
        }

        .search-box:focus {
            outline: none;
            border-color: var(--primary-blue);
            background: white;
            box-shadow: 0 0 0 4px rgba(33, 150, 243, 0.1);
        }

        .clear-search {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            border: none;
            background: var(--light-blue);
            color: var(--primary-blue);
            cursor: pointer;
            padding: 0.4rem 0.8rem;
            display: none;
            font-size: 0.85rem;
            border-radius: 6px;
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .clear-search:hover {
            background: var(--primary-blue);
            color: white;
        }

        .clear-search:active {
            transform: translateY(-50%) scale(0.95);
        }

        .total-badge {
            background: var(--light-blue);
            color: var(--primary-blue);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 500;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .data-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            overflow-x: auto;
        }

        .table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }

        .table thead th {
            background: var(--primary-blue);
            color: white;
            padding: 1rem;
            font-weight: 500;
            text-align: left;
            font-size: 0.95rem;
            white-space: nowrap;
            cursor: pointer;
            user-select: none;
            position: relative;
        }

        .table thead th:hover {
            background: var(--hover-blue);
        }

        .table thead th::after {
            content: '↕';
            position: absolute;
            right: 8px;
            opacity: 0.5;
        }

        .table thead th.sort-asc::after {
            content: '↑';
            opacity: 1;
        }

        .table thead th.sort-desc::after {
            content: '↓';
            opacity: 1;
        }

        .table tbody td {
            padding: 0.5rem;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-dark);
            font-size: 0.95rem;
            white-space: nowrap;
            line-height: 1;
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        .table tbody tr:hover {
            background: var(--background);
        }

        .editable-cell {
            position: relative;
        }

        .editable-content {
            min-width: 150px;
            padding: 0.25rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            cursor: pointer;
            background-color: #f8f9fa;
            line-height: 1;
        }

        .editable-content:hover {
            border-color: var(--primary-blue);
            background-color: #fff;
            box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
        }

        .editable-content:focus {
            outline: none;
            border-color: var(--primary-blue);
            background-color: #fff;
            box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
        }

        .status-select {
            padding: 0.25rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background-color: #f8f9fa;
            cursor: pointer;
            width: 100px;
            height: 24px;
        }

        .status-select:hover, .status-select:focus {
            border-color: var(--primary-blue);
            background-color: #fff;
            box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
            outline: none;
        }

        .btn-update {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.9rem;
            text-decoration: none;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            font-weight: 500;
            background: var(--light-blue);
            color: var(--primary-blue);
            border: 1px solid transparent;
            cursor: pointer;
            height: 24px;
        }

        .btn-update:hover {
            background: var(--primary-blue);
            color: white;
        }

        @media (max-width: 1024px) {
            .report-container {
                padding: 1rem;
            }

            .header-section {
                padding: 1rem;
            }

            .data-table {
                overflow-x: auto;
            }
        }

        @media (max-width: 768px) {
            .header-section {
                flex-direction: column;
                align-items: stretch;
            }

            .search-section {
                order: 2;
            }

            .total-badge {
                align-self: flex-start;
            }
        }

        @media (max-width: 1200px) {
            .search-section {
                flex-direction: column;
            }

            .search-container {
                max-width: none;
            }

            .upload-section {
                width: 100%;
            }

            .file-input {
                width: 100%;
            }
        }

        .action-btns {
            display: flex;
            gap: 0.5rem;
            white-space: nowrap;
        }

        /* Pagination Styles */
        .pagination-controls {
            margin-top: 1rem;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        }

        .pagination {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .page-link {
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            color: var(--text-dark);
            background: var(--background);
            transition: all 0.2s ease;
        }

        .page-link:hover {
            background: var(--light-blue);
            color: var(--primary-blue);
        }

        .page-link.active {
            background: var(--primary-blue);
            color: white;
        }

        @media (max-width: 768px) {
            .filters-grid {
                flex-direction: column;
            }

            .filter-group {
                width: 100%;
            }

            .pagination {
                flex-wrap: wrap;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Main content container -->
    <div id="mainContent">
        <!-- Report Component -->
        <div class="report-container">
            <div id="successAlert" class="success-alert">
                <i class="fas fa-check-circle"></i>
                <span id="alertMessage"></span>
            </div>
            
            <div class="header-section">
                <h1 class="page-title">Tracking Report</h1>
                <div class="search-section">
                    <div class="upload-section">
                        <form id="uploadForm" enctype="multipart/form-data" class="upload-form">
                            <input type="file" id="excelFile" name="excelFile" accept=".xlsx, .xls" class="file-input" />
                            <button type="submit" class="btn-upload">
                                <i class="fas fa-upload"></i>
                                Upload
                            </button>
                        </form>
                    </div>
                </div>
                <div class="total-badge">
                    <i class="fas fa-file-alt"></i>
                    <span>Total Records: <span id="recordCount"><?php echo $total_records; ?></span></span>
                </div>
            </div>
            
            <div class="header-section">
                <h2 class="section-title">Filters</h2>
                <div class="filters-section">
                    <div class="filters-grid">
                        <div class="filter-group">
                            <label for="month">Month</label>
                            <select id="month" name="month" class="filter-input">
                                <?php foreach ($months as $month): ?>
                                    <option value="<?php echo htmlspecialchars($month['value']); ?>" 
                                            <?php echo $selected_month === $month['value'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($month['label']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="status">Status</label>
                            <select id="status" name="status" class="filter-input">
                                <option value="">All Status</option>
                                <option value="Open">Open</option>
                                <option value="Closed">Closed</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label>&nbsp;</label>
                            <a href="index.php?page=tracking_report" class="btn-reset">Reset Filters</a>
                        </div>

                        <div class="filter-group search-container">
                            <label for="searchBox">Search Docket</label>
                            <div class="search-input-wrapper">
                                <input type="text" 
                                       id="searchBox" 
                                       class="search-box" 
                                       placeholder="Search by docket number..."
                                       autocomplete="off">
                                <button type="button" id="clearSearch" class="clear-search">
                                    Clear
                                </button>
                            </div>
                            <div id="loadingIndicator" class="loading-indicator"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="header-section1">
                <a href="upload/tracking_upload_template.xlsx" download class="btn-download">
                    <i class="fas fa-download"></i>
                    Download tracking upload template
                </a>
                <a href="pages/download_open_dockets.php" class="btn-download">
                    <i class="fas fa-download"></i>
                    Download list of dockets for bulk tracking
                </a>
            </div>
            
            <div class="data-table">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Entry Type</th>
                            <th>Customer</th>
                            <th>Docket No.</th>
                            <th>Date</th>
                            <th>Mode</th>
                            <th>Destination</th>
                            <th>EDD</th>
                            <th>Delivery Status</th>
                            <th>Delivery Date</th>
                            <th>Final Comments</th>
                            <th>Open/Closed</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="trackingTableBody">
                        <?php while ($row = $result->fetch_assoc()): ?>
                        <tr data-id="<?php echo $row['id']; ?>">
                            <td data-label="entry type"><?php echo htmlspecialchars($row['tr_entry_type']); ?></td>
                            <td data-label="customer"><?php echo htmlspecialchars($row['tr_cust']); ?></td>
                            <td data-label="docket no"><?php echo htmlspecialchars($row['tr_docket_no']); ?></td>
                            <td data-label="date"><?php echo date('d-M-Y', strtotime($row['tr_docket_dt'])); ?></td>
                            <td data-label="mode"><?php echo htmlspecialchars($row['tr_mot']); ?></td>
                            <td data-label="destination"><?php echo htmlspecialchars($row['tr_dest']); ?></td>
                            <td data-label="edd"><?php echo $row['tr_edd'] ? date('d-M-Y', strtotime($row['tr_edd'])) : ''; ?></td>
                            <td data-label="delivery status"><?php echo htmlspecialchars($row['tr_delsts']); ?></td>
                            <td data-label="delivery date">
                                <?php echo $row['tr_lststs_dt'] ? date('d-M-Y', strtotime($row['tr_lststs_dt'])) : ''; ?>
                            </td>
                            <td data-label="comments" class="editable-cell">
                                <div class="editable-content" contenteditable="true">
                                    <?php echo htmlspecialchars($row['tr_comment']); ?>
                                </div>
                            </td>
                            <td data-label="status">
                                <select class="status-select">
                                    <option value="Open" <?php echo $row['tr_opcl'] === 'Open' ? 'selected' : ''; ?>>Open</option>
                                    <option value="Closed" <?php echo $row['tr_opcl'] === 'Closed' ? 'selected' : ''; ?>>Closed</option>
                                </select>
                            </td>
                            <td data-label="actions">
                                <div class="action-btns">
                                    <button type="button" class="btn-update">
                                        <i class="fas fa-save"></i>
                                        Update
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination Controls -->
            <div class="pagination-controls">
                <?php if ($total_pages > 1): ?>
                    <div class="pagination">
                        <?php if ($page > 1): ?>
                            <a href="?page=tracking_report&month=<?php echo $selected_month; ?>&status=<?php echo $selected_status; ?>&page_num=1" class="page-link">First</a>
                            <a href="?page=tracking_report&month=<?php echo $selected_month; ?>&status=<?php echo $selected_status; ?>&page_num=<?php echo ($page - 1); ?>" class="page-link">Previous</a>
                        <?php endif; ?>

                        <?php
                        $start_page = max(1, $page - 2);
                        $end_page = min($total_pages, $page + 2);

                        for ($i = $start_page; $i <= $end_page; $i++): ?>
                            <a href="?page=tracking_report&month=<?php echo $selected_month; ?>&status=<?php echo $selected_status; ?>&page_num=<?php echo $i; ?>" 
                               class="page-link <?php echo $i === $page ? 'active' : ''; ?>">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                            <a href="?page=tracking_report&month=<?php echo $selected_month; ?>&status=<?php echo $selected_status; ?>&page_num=<?php echo ($page + 1); ?>" class="page-link">Next</a>
                            <a href="?page=tracking_report&month=<?php echo $selected_month; ?>&status=<?php echo $selected_status; ?>&page_num=<?php echo $total_pages; ?>" class="page-link">Last</a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
    $(document).ready(function() {
        const loadingOverlay = document.getElementById('loadingOverlay');
        const mainContent = document.getElementById('mainContent');
        const reportContainer = document.querySelector('.report-container');
        
        function showContent() {
            loadingOverlay.style.opacity = '0';
            setTimeout(() => {
                loadingOverlay.style.visibility = 'hidden';
                mainContent.style.visibility = 'visible';
                mainContent.style.opacity = '1';
                document.body.style.overflow = 'auto';
                setTimeout(() => {
                    reportContainer.style.opacity = '1';
                }, 50);
            }, 300);
        }
        
        if (document.readyState === 'complete') {
            showContent();
        } else {
            window.addEventListener('load', showContent);
        }

        function showMessage(message, isError = false) {
            const messageElement = $('#successMessage');
            messageElement.text(message);
            messageElement.css('background-color', isError ? '#DC2626' : '#4CAF50');
            messageElement.addClass('show-message');
            setTimeout(() => {
                messageElement.removeClass('show-message');
                setTimeout(() => {
                    messageElement.hide();
                }, 500);
            }, 3000);
        }

        function showSuccessAlert(message) {
            const alertElement = $('#successAlert');
            $('#alertMessage').text(message);
            alertElement.addClass('show');
            
            setTimeout(() => {
                alertElement.removeClass('show');
            }, 3000);
        }

        // Handle update button click
        $('.table').on('click', '.btn-update', function() {
            const row = $(this).closest('tr');
            const id = row.data('id');
            const comment = row.find('.editable-content').text().trim();
            const status = row.find('.status-select').val();
            const button = $(this);

            $.ajax({
                url: 'index.php?page=tracking_report',
                method: 'POST',
                data: {
                    update_id: id,
                    comment: comment,
                    status: status
                },
                dataType: 'json',
                beforeSend: function() {
                    button.prop('disabled', true);
                },
                success: function(response) {
                    if (response.success) {
                        showSuccessAlert(response.message || 'Record updated successfully!');
                    } else {
                        showMessage(response.message || 'Update failed', true);
                    }
                },
                error: function(xhr) {
                    let errorMessage = 'Error updating record';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        errorMessage = response.message || errorMessage;
                    } catch(e) {
                        console.error('Error parsing response:', xhr.responseText);
                    }
                    showMessage(errorMessage, true);
                },
                complete: function() {
                    button.prop('disabled', false);
                }
            });
        });

        let searchTimer;
        const searchBox = $('#searchBox');
        const clearButton = $('#clearSearch');
        const loadingIndicator = $('#loadingIndicator');
        const tbody = $('#trackingTableBody');
        let currentSort = { column: null, direction: 'asc' };
        
        // Add click handlers to table headers
        $('.table thead th').each(function(index) {
            $(this).on('click', function() {
                const column = $(this).text().toLowerCase().trim();
                
                $('.table thead th').removeClass('sort-asc sort-desc');
                
                if (currentSort.column === column) {
                    currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
                } else {
                    currentSort.column = column;
                    currentSort.direction = 'asc';
                }
                
                $(this).addClass(`sort-${currentSort.direction}`);
                sortTable(column, currentSort.direction);
            });
        });
        
        function sortTable(column, direction) {
            const rows = tbody.find('tr').toArray();
            const multiplier = direction === 'asc' ? 1 : -1;
            
            rows.sort((a, b) => {
                let aValue = $(a).find(`td[data-label="${column}"]`).text().trim();
                let bValue = $(b).find(`td[data-label="${column}"]`).text().trim();
                
                if (['date', 'edd', 'delivery date'].includes(column)) {
                    aValue = aValue ? new Date(aValue.split('-').reverse().join('-')) : new Date(0);
                    bValue = bValue ? new Date(bValue.split('-').reverse().join('-')) : new Date(0);
                } else {
                    aValue = aValue.toLowerCase();
                    bValue = bValue.toLowerCase();
                }
                
                if (aValue < bValue) return -1 * multiplier;
                if (aValue > bValue) return 1 * multiplier;
                return 0;
            });
            
            tbody.empty().append(rows);
        }

        function showError(message) {
            tbody.empty();
            tbody.append(`
                <tr>
                    <td colspan="12" style="text-align: center; padding: 2rem;">
                        <div style="color: #DC2626;">${message}</div>
                    </td>
                </tr>
            `);
        }

        // Search functionality
        searchBox.on('input', function() {
            const searchValue = $(this).val().trim();
            clearButton.toggle(searchValue.length > 0);
            
            clearTimeout(searchTimer);
            
            if (searchValue.length > 0) {
                loadingIndicator.show();
                
                searchTimer = setTimeout(() => {
                    loadSearchResults(searchValue, 1);
                }, 300);
            } else {
                location.reload();
            }
        });

        // Handle month filter change
        $('#month').on('change', function() {
            const searchValue = $('#searchBox').val().trim();
            loadSearchResults(searchValue, 1);
        });

        // Handle status filter change
        $('#status').on('change', function() {
            const searchValue = $('#searchBox').val().trim();
            loadSearchResults(searchValue, 1);
        });

        // Clear search
        $('#clearSearch').on('click', function() {
            $('#searchBox').val('');
            $(this).hide();
            loadSearchResults('', 1);
        });

        // Function to load search results with pagination
        function loadSearchResults(searchValue, page) {
            const month = $('#month').val();
            const status = $('#status').val();
            
            $.ajax({
                url: 'index.php?page=tracking_report&ajax_search=1',
                data: { 
                    search: searchValue,
                    month: month,
                    status: status,
                    page_num: page
                },
                method: 'GET',
                success: function(response) {
                    if (response.success) {
                        tbody.empty();
                        $('#recordCount').text(response.total);
                        
                        if (response.data.length === 0) {
                            showError('No matching records found');
                            return;
                        }
                        
                        response.data.forEach(row => {
                            tbody.append(`
                                <tr data-id="${row.id}">
                                    <td data-label="entry type">${row.tr_entry_type}</td>
                                    <td data-label="customer">${row.tr_cust}</td>
                                    <td data-label="docket no">${row.tr_docket_no}</td>
                                    <td data-label="date">${row.tr_docket_dt}</td>
                                    <td data-label="mode">${row.tr_mot}</td>
                                    <td data-label="destination">${row.tr_dest}</td>
                                    <td data-label="edd">${row.tr_edd}</td>
                                    <td data-label="delivery status">${row.tr_delsts}</td>
                                    <td data-label="delivery date">${row.tr_lststs_dt}</td>
                                    <td data-label="comments" class="editable-cell">
                                        <div class="editable-content" contenteditable="true">${row.tr_comment}</div>
                                    </td>
                                    <td data-label="status">
                                        <select class="status-select">
                                            <option value="Open" ${row.tr_opcl === 'Open' ? 'selected' : ''}>Open</option>
                                            <option value="Closed" ${row.tr_opcl === 'Closed' ? 'selected' : ''}>Closed</option>
                                        </select>
                                    </td>
                                    <td data-label="actions">
                                        <div class="action-btns">
                                            <button type="button" class="btn-update">
                                                <i class="fas fa-save"></i>
                                                Update
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            `);
                        });

                        // Update pagination
                        updatePagination(response.current_page, response.total_pages, searchValue);
                    } else {
                        showError('Error loading data');
                    }
                },
                error: function() {
                    showError('Error loading data');
                },
                complete: function() {
                    loadingIndicator.hide();
                }
            });
        }

        // Function to update pagination controls
        function updatePagination(currentPage, totalPages, searchValue) {
            const paginationDiv = $('.pagination');
            const month = $('#month').val();
            const status = $('#status').val();
            paginationDiv.empty();

            if (totalPages > 1) {
                if (currentPage > 1) {
                    paginationDiv.append(`
                        <a href="#" class="page-link" data-page="1">First</a>
                        <a href="#" class="page-link" data-page="${currentPage - 1}">Previous</a>
                    `);
                }

                const startPage = Math.max(1, currentPage - 2);
                const endPage = Math.min(totalPages, currentPage + 2);

                for (let i = startPage; i <= endPage; i++) {
                    paginationDiv.append(`
                        <a href="#" class="page-link ${i === currentPage ? 'active' : ''}" data-page="${i}">${i}</a>
                    `);
                }

                if (currentPage < totalPages) {
                    paginationDiv.append(`
                        <a href="#" class="page-link" data-page="${currentPage + 1}">Next</a>
                        <a href="#" class="page-link" data-page="${totalPages}">Last</a>
                    `);
                }
            }
        }

        // Handle pagination clicks
        $(document).on('click', '.page-link', function(e) {
            e.preventDefault();
            const page = $(this).data('page');
            const searchValue = $('#searchBox').val().trim();
            loadSearchResults(searchValue, page);
        });

        // Handle Excel upload
        $('#uploadForm').on('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            $.ajax({
                url: 'pages/upload_tracking.php',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                beforeSend: function() {
                    loadingOverlay.style.visibility = 'visible';
                    loadingOverlay.style.opacity = '1';
                },
                success: function(response) {
                    const result = JSON.parse(response);
                    if (result.success) {
                        showMessage(result.message);
                        location.reload(); // Reload to show updated data
                    } else {
                        showMessage(result.message, true);
                    }
                },
                error: function() {
                    showMessage('Error uploading file', true);
                },
                complete: function() {
                    loadingOverlay.style.opacity = '0';
                    setTimeout(() => {
                        loadingOverlay.style.visibility = 'hidden';
                    }, 300);
                }
            });
        });
    });
    </script>
</body>
</html> 