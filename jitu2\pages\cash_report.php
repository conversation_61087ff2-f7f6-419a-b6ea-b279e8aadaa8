<?php
session_start();
include 'db_connect.php';

if (!isset($_SESSION['username'])) {
    header("Location: login.php");
    exit();
}

$username = $_SESSION['username'];

// Handle AJAX search
if (isset($_GET['ajax_search'])) {
    try {
        $username = $_SESSION['username'];
        $search_query = $_GET['search'] ?? '';
        $selected_month = $_GET['month'] ?? date('m-Y');

        $data = [];

        // Build base query with month filter
        $sql = "SELECT
                    docket_no,
                    docket_date,
                    pincode,
                    destination,
                    weight,
                    mode_of_tsp,
                    remarks,
                    amount,
                    payment_status,
                    payment_received_date
                FROM transactions
                WHERE username = ?
                AND entry_type = 'cash'";

        $params = [$username];
        $types = "s";

        // Add month filter
        $month_year = explode('-', $selected_month);
        $sql .= " AND MONTH(docket_date) = ? AND YEAR(docket_date) = ?";
        $params[] = $month_year[0];
        $params[] = $month_year[1];
        $types .= "ii";

        // Add search filter if provided
        if (!empty($search_query)) {
            $sql .= " AND (docket_no LIKE ? OR destination LIKE ? OR pincode LIKE ?)";
            $search_pattern = "%{$search_query}%";
            $params[] = $search_pattern;
            $params[] = $search_pattern;
            $params[] = $search_pattern;
            $types .= "sss";
        }

        $sql .= " ORDER BY docket_date DESC";

        $stmt = $conn->prepare($sql);
        $stmt->bind_param($types, ...$params);

        if (!$stmt->execute()) {
            throw new Exception("Database error: " . $stmt->error);
        }

        $result = $stmt->get_result();

        while ($row = $result->fetch_assoc()) {
            $data[] = [
                'docket_no' => htmlspecialchars($row['docket_no']),
                'docket_date' => date('d M Y', strtotime($row['docket_date'])),
                'pincode' => htmlspecialchars($row['pincode']),
                'destination' => htmlspecialchars($row['destination']),
                'weight' => number_format($row['weight'], 2),
                'mode_of_tsp' => htmlspecialchars($row['mode_of_tsp']),
                'remarks' => htmlspecialchars($row['remarks']),
                'amount' => number_format($row['amount'], 2),
                'payment_status' => htmlspecialchars($row['payment_status']),
                'payment_received_date' => $row['payment_received_date'] ? date('d M Y', strtotime($row['payment_received_date'])) : null
            ];
        }

        ob_clean();
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'data' => $data,
            'total' => count($data)
        ]);

    } catch (Exception $e) {
        ob_clean();
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    } finally {
        if (isset($stmt)) $stmt->close();
        if (isset($conn)) $conn->close();
    }
    exit;
}

// Initial data load
$username = $_SESSION['username'];

// Get filter values
$current_month = date('m-Y');
$selected_month = $_GET['month'] ?? $current_month;

// Generate last 12 months for dropdown
$months = [];
for ($i = 0; $i < 12; $i++) {
    $date = strtotime("-$i months");
    $month_num = date('m', $date);
    $year = date('Y', $date);
    $month_name = date('M', $date);
    $months[] = [
        'value' => $month_num . '-' . $year,
        'label' => $month_name . '-' . $year
    ];
}

// Build the query with month filter
$sql = "SELECT
            docket_no,
            docket_date,
            pincode,
            destination,
            weight,
            mode_of_tsp,
            remarks,
            amount,
            payment_status,
            payment_received_date
        FROM transactions
        WHERE username = ?
        AND entry_type = 'cash'";

$params = [$username];
$types = "s";

// Always filter by month (current month by default)
$month_year = explode('-', $selected_month);
$sql .= " AND MONTH(docket_date) = ? AND YEAR(docket_date) = ?";
$params[] = $month_year[0];
$params[] = $month_year[1];
$types .= "ii";

$sql .= " ORDER BY docket_date DESC";

$stmt = $conn->prepare($sql);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$result = $stmt->get_result();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Inline Critical CSS -->
    <style>
        /* Critical styles that should load first */
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            overflow: hidden;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #2196F3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        #mainContent {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease-in;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .report-container {
            opacity: 0;
            transition: opacity 0.3s ease-in;
        }

        .success-alert {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            background-color: #4CAF50;
            color: white;
            border-radius: 4px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            display: none;
            animation: slideInRight 0.5s ease-out;
            font-size: 16px;
            font-weight: 500;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .success-alert.show {
            display: block;
        }

        .success-alert i {
            margin-right: 8px;
        }
    </style>

    <!-- Deferred non-critical CSS -->
    <style>
        :root {
            --primary-blue: #2196F3;
            --light-blue: #E3F2FD;
            --hover-blue: #1976D2;
            --sky-blue: #87CEEB;
            --text-dark: #2c3e50;
            --border-color: #e0e0e0;
            --background: #F8FAFC;
        }

        .report-container {
            padding: 2rem;
            margin: 0 auto;
            max-width: 2000px;
            position: relative;
            margin-left: 0px;
            margin-top: 0px;
        }

        .header-section {
            background: white;
            padding: 0.75rem 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            margin-bottom: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .page-title {
            color: var(--primary-blue);
            font-size: 1.75rem;
            font-weight: 600;
            margin: 0;
        }

        .search-container {
            position: relative;
            flex: 1;
            max-width: 500px;
        }

        .search-box {
            width: 100%;
            padding: 0.75rem 3.5rem 0.75rem 1rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: var(--background);
        }

        .search-box:focus {
            outline: none;
            border-color: var(--primary-blue);
            background: white;
            box-shadow: 0 0 0 4px rgba(33, 150, 243, 0.1);
        }

        .clear-search {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            border: none;
            background: var(--light-blue);
            color: var(--primary-blue);
            cursor: pointer;
            padding: 0.4rem 0.8rem;
            display: none;
            font-size: 0.85rem;
            border-radius: 6px;
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .clear-search:hover {
            background: var(--primary-blue);
            color: white;
        }

        .total-badge {
            background: var(--light-blue);
            color: var(--primary-blue);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 500;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .data-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            overflow-x: auto;
        }

        .table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }

        .table thead th {
            background: var(--primary-blue);
            color: white;
            padding: 1rem;
            font-weight: 500;
            text-align: left;
            font-size: 0.95rem;
            white-space: nowrap;
            cursor: pointer;
            user-select: none;
            position: relative;
        }

        .table thead th:hover {
            background: var(--hover-blue);
        }

        .table thead th::after {
            content: '↕';
            position: absolute;
            right: 8px;
            opacity: 0.5;
        }

        .table thead th.sort-asc::after {
            content: '↑';
            opacity: 1;
        }

        .table thead th.sort-desc::after {
            content: '↓';
            opacity: 1;
        }

        .table tbody td {
            padding: 0.5rem;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-dark);
            font-size: 0.95rem;
            white-space: nowrap;
            line-height: 1;
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        .table tbody tr:hover {
            background: var(--background);
        }

        .status-badge {
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.4rem;
        }

        .status-badge.pending {
            background: #fff5f7;
            color: #ef476f;
        }

        .status-badge.cash-received {
            background: #f0faf9;
            color: #2ec4b6;
        }

        .status-badge.online-received {
            background: #e3f2fd;
            color: #2196F3;
        }

        @media (max-width: 1024px) {
            .report-container {
                padding: 1rem;
            }

            .header-section {
                padding: 1rem;
            }

            .data-table {
                overflow-x: auto;
            }
        }

        @media (max-width: 768px) {
            .header-section {
                flex-direction: column;
                align-items: stretch;
            }

            .search-container {
                order: 2;
            }

            .total-badge {
                align-self: flex-start;
            }
        }

        /* Filter Styles */
        .filters-section {
            width: 100%;
            margin-bottom: 1rem;
            background: white;
            padding: 1rem 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .filter-group label {
            font-size: 0.9rem;
            color: var(--text-dark);
            font-weight: 500;
        }

        .filter-input {
            padding: 0.75rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: var(--background);
        }

        .filter-input:focus {
            outline: none;
            border-color: var(--primary-blue);
            background: white;
            box-shadow: 0 0 0 4px rgba(33, 150, 243, 0.1);
        }

        .filter-buttons {
            display: flex;
            gap: 0.5rem;
            align-items: flex-end;
        }

        .btn-apply, .btn-reset {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            text-align: center;
        }

        .btn-apply {
            background: var(--primary-blue);
            color: white;
            border: none;
        }

        .btn-apply:hover {
            background: var(--hover-blue);
        }

        .btn-reset {
            background: var(--light-blue);
            color: var(--primary-blue);
            border: 1px solid transparent;
        }

        .btn-reset:hover {
            background: var(--primary-blue);
            color: white;
        }

        @media (max-width: 768px) {
            .filters-grid {
                grid-template-columns: 1fr;
            }

            .filter-buttons {
                flex-direction: column;
            }

            .btn-apply, .btn-reset {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Main content container -->
    <div id="mainContent">
        <!-- Report Component -->
        <div class="report-container">
            <div id="successAlert" class="success-alert">
                <i class="fas fa-check-circle"></i>
                <span id="alertMessage"></span>
            </div>

            <div class="header-section">
                <h1 class="page-title">Cash Transactions Report</h1>
                <div class="filters-section">
                    <form id="filterForm" method="GET" action="index.php" class="filters-grid">
                        <input type="hidden" name="page" value="cash_report">

                        <div class="filter-group">
                            <label for="month">Month</label>
                            <select id="month" name="month" class="filter-input">
                                <?php foreach ($months as $month): ?>
                                    <option value="<?php echo $month['value']; ?>" <?php echo $selected_month === $month['value'] ? 'selected' : ''; ?>>
                                        <?php echo $month['label']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="filter-group filter-buttons">
                            <button type="submit" class="btn-apply">Apply Filter</button>
                            <a href="index.php?page=cash_report" class="btn-reset">Reset</a>
                        </div>
                    </form>
                </div>
                <div class="search-container">
                    <input type="text"
                           id="searchBox"
                           class="search-box"
                           placeholder="Search by docket number, destination, or pincode..."
                           autocomplete="off">
                    <button type="button" id="clearSearch" class="clear-search">
                        Clear
                    </button>
                    <div id="loadingIndicator" class="loading-indicator"></div>
                </div>
                <div class="total-badge">
                    <i class="fas fa-file-alt"></i>
                    <span>Total Records: <span id="recordCount"><?php echo $result->num_rows; ?></span></span>
                </div>
            </div>

            <div class="data-table">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Docket No.</th>
                            <th>Date</th>
                            <th>Pincode</th>
                            <th>Destination</th>
                            <th>Weight</th>
                            <th>Mode</th>
                            <th>Remarks</th>
                            <th>Amount</th>
                            <th>Payment Date</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody id="cashTableBody">
                        <?php while ($row = $result->fetch_assoc()): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($row['docket_no']); ?></td>
                            <td><?php echo date('d-M-Y', strtotime($row['docket_date'])); ?></td>
                            <td><?php echo htmlspecialchars($row['pincode']); ?></td>
                            <td><?php echo htmlspecialchars($row['destination']); ?></td>
                            <td><?php echo number_format($row['weight'], 2); ?></td>
                            <td><?php echo htmlspecialchars($row['mode_of_tsp']); ?></td>
                            <td><?php echo htmlspecialchars($row['remarks']); ?></td>
                            <td>₹<?php echo number_format($row['amount'], 2); ?></td>
                            <td><?php echo $row['payment_received_date'] ? date('d-M-Y', strtotime($row['payment_received_date'])) : '-'; ?></td>
                            <td>
                                <span class="status-badge <?php echo strtolower(str_replace('-', '-', $row['payment_status'])); ?>">
                                    <?php echo htmlspecialchars($row['payment_status']); ?>
                                </span>
                            </td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
    $(document).ready(function() {
        const loadingOverlay = document.getElementById('loadingOverlay');
        const mainContent = document.getElementById('mainContent');
        const reportContainer = document.querySelector('.report-container');

        function showContent() {
            loadingOverlay.style.opacity = '0';
            setTimeout(() => {
                loadingOverlay.style.visibility = 'hidden';
                mainContent.style.visibility = 'visible';
                mainContent.style.opacity = '1';
                document.body.style.overflow = 'auto';
                setTimeout(() => {
                    reportContainer.style.opacity = '1';
                }, 50);
            }, 300);
        }

        if (document.readyState === 'complete') {
            showContent();
        } else {
            window.addEventListener('load', showContent);
        }

        let searchTimer;
        const searchBox = $('#searchBox');
        const clearButton = $('#clearSearch');
        const loadingIndicator = $('#loadingIndicator');
        const tbody = $('#cashTableBody');
        let currentSort = { column: null, direction: 'asc' };

        // Add click handlers to table headers
        $('.table thead th').each(function(index) {
            $(this).on('click', function() {
                const column = $(this).text().toLowerCase().trim();

                $('.table thead th').removeClass('sort-asc sort-desc');

                if (currentSort.column === column) {
                    currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
                } else {
                    currentSort.column = column;
                    currentSort.direction = 'asc';
                }

                $(this).addClass(`sort-${currentSort.direction}`);
                sortTable(column, currentSort.direction);
            });
        });

        function sortTable(column, direction) {
            const rows = tbody.find('tr').toArray();
            const multiplier = direction === 'asc' ? 1 : -1;

            rows.sort((a, b) => {
                let aValue = $(a).find(`td:nth-child(${getColumnIndex(column)})`).text().trim();
                let bValue = $(b).find(`td:nth-child(${getColumnIndex(column)})`).text().trim();

                if (['date'].includes(column)) {
                    aValue = aValue ? new Date(aValue.split('-').reverse().join('-')) : new Date(0);
                    bValue = bValue ? new Date(bValue.split('-').reverse().join('-')) : new Date(0);
                } else if (['amount', 'weight'].includes(column)) {
                    aValue = parseFloat(aValue.replace(/[^0-9.-]+/g, '')) || 0;
                    bValue = parseFloat(bValue.replace(/[^0-9.-]+/g, '')) || 0;
                } else {
                    aValue = aValue.toLowerCase();
                    bValue = bValue.toLowerCase();
                }

                if (aValue < bValue) return -1 * multiplier;
                if (aValue > bValue) return 1 * multiplier;
                return 0;
            });

            tbody.empty().append(rows);
        }

        function getColumnIndex(column) {
            const columns = {
                'docket no.': 1,
                'date': 2,
                'pincode': 3,
                'destination': 4,
                'weight': 5,
                'mode': 6,
                'remarks': 7,
                'amount': 8,
                'payment date': 9,
                'status': 10
            };
            return columns[column] || 1;
        }

        function showError(message) {
            tbody.empty();
            tbody.append(`
                <tr>
                    <td colspan="9" style="text-align: center; padding: 2rem;">
                        <div style="color: #DC2626;">${message}</div>
                    </td>
                </tr>
            `);
        }

        // Search functionality
        searchBox.on('input', function() {
            const searchValue = $(this).val().trim();
            clearButton.toggle(searchValue.length > 0);

            clearTimeout(searchTimer);

            if (searchValue.length > 0) {
                loadingIndicator.show();

                searchTimer = setTimeout(() => {
                    const selectedMonth = $('#month').val();
                    $.ajax({
                        url: 'index.php?page=cash_report&ajax_search=1',
                        data: {
                            search: searchValue,
                            month: selectedMonth
                        },
                        method: 'GET',
                        success: function(response) {
                            if (response.success) {
                                tbody.empty();
                                $('#recordCount').text(response.total);

                                if (response.data.length === 0) {
                                    showError('No matching records found');
                                    return;
                                }

                                response.data.forEach(row => {
                                    tbody.append(`
                                        <tr>
                                            <td>${row.docket_no}</td>
                                            <td>${row.docket_date}</td>
                                            <td>${row.pincode}</td>
                                            <td>${row.destination}</td>
                                            <td>${row.weight}</td>
                                            <td>${row.mode_of_tsp}</td>
                                            <td>${row.remarks}</td>
                                            <td>₹${row.amount}</td>
                                            <td>${row.payment_received_date ? new Date(row.payment_received_date).toLocaleDateString('en-IN', {day: '2-digit', month: 'short', year: 'numeric'}) : '-'}</td>
                                            <td>
                                                <span class="status-badge ${row.payment_status.toLowerCase().replace('-', '-')}">
                                                    ${row.payment_status}
                                                </span>
                                            </td>
                                        </tr>
                                    `);
                                });
                            } else {
                                showError('Error loading data');
                            }
                        },
                        error: function() {
                            showError('Error loading data');
                        },
                        complete: function() {
                            loadingIndicator.hide();
                        }
                    });
                }, 300);
            } else {
                location.reload();
            }
        });

        // Clear search
        clearButton.on('click', function() {
            searchBox.val('').trigger('input');
        });
    });
    </script>
</body>
</html>