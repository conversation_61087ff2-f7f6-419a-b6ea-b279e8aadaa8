<?php
session_start();

$page = isset($_GET['page']) ? preg_replace('/[^a-zA-Z0-9_.-]/', '', $_GET['page']) : 'dashboard';
$pageFile = 'pages/' . $page . (strpos($page, '.php') === false ? '.php' : '');

// Simple page titles
$pageTitles = [
    'dashboard' => ['Dashboard', 'Overview of your billing system'],
    'cash-entry' => ['Cash Entry', 'Record new cash transactions'],
    'pending_cash' => ['Pending Cash', 'Update pending cash transactions'],
    'credit-entry' => ['Credit Entry', 'Manage credit transactions'],
    'report' => ['Entry Report', 'View and analyze entries'],
    'cn-entry' => ['C-Note Entry', 'Create new consignment notes'],
    'cn_report' => ['C-Note Report', 'View consignment reports'],
    'available_cn_report' => ['Available CN Report', 'Track available consignment numbers'],
    'customer_dashboard' => ['Customer Dashboard', 'Manage customer information'],
    'rate_master' => ['Rate Master', 'Configure pricing rates'],
    'invoice_dashboard' => ['Invoice Dashboard', 'Manage billing invoices'],
    'transaction_form.php' => ['Transaction Form', 'Create and edit transactions']
];

$currentPage = $pageTitles[$page] ?? ['Page Not Found', 'The requested page could not be found'];
?>

<style>
    .main-content {
        margin-left: 280px;
        padding: 2rem;
        min-height: calc(100vh - var(--header-height));
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%);
        transition: all var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        overflow-x: auto;
    }

    .main-content::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 100%;
        background:
            radial-gradient(circle at top right, rgba(99, 102, 241, 0.08), transparent 50%),
            radial-gradient(circle at bottom left, rgba(139, 92, 246, 0.08), transparent 50%);
        pointer-events: none;
    }

    .content-body {
        background: var(--glass-bg);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid var(--glass-border);
        border-radius: 20px;
        padding: 2rem;
        box-shadow: var(--glass-shadow);
        position: relative;
        overflow: hidden;
        min-height: calc(100vh - var(--header-height) - 4rem);
        animation: fadeIn 0.5s ease-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .content-body::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg,
            var(--accent-color) 0%,
            var(--success-color) 25%,
            var(--warning-color) 50%,
            var(--danger-color) 75%,
            var(--primary-color) 100%
        );
        opacity: 0.8;
    }

    .message {
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
        border-radius: 12px;
        display: flex;
        align-items: center;
        gap: 1rem;
        animation: slideIn 0.3s ease-out;
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
    }

    .message::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        opacity: 0.8;
    }

    .message-success::before {
        background: linear-gradient(90deg, #22c55e, #10b981);
    }

    .message-error::before {
        background: linear-gradient(90deg, #ef4444, #dc2626);
    }

    @keyframes slideIn {
        from {
            transform: translateY(-10px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    .message-success {
        background: rgba(34, 197, 94, 0.1);
        border: 1px solid rgba(34, 197, 94, 0.2);
        color: #166534;
    }

    .message-error {
        background: rgba(239, 68, 68, 0.1);
        border: 1px solid rgba(239, 68, 68, 0.2);
        color: #991b1b;
    }

    .message i {
        font-size: 1.25rem;
    }

    .message-success i {
        color: #22c55e;
    }

    .message-error i {
        color: #ef4444;
    }

    .page-not-found {
        text-align: center;
        padding: 4rem 2rem;
        position: relative;
        animation: fadeIn 0.5s ease-out;
    }

    .page-not-found i {
        font-size: 4rem;
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 1rem;
    }

    .page-not-found h2 {
        font-size: 1.75rem;
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 0.75rem;
        font-weight: 600;
    }

    .page-not-found p {
        color: var(--text-muted);
        margin-bottom: 2rem;
        font-size: 1.1rem;
    }

    .page-not-found a {
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.875rem 1.75rem;
        background: var(--gradient-primary);
        color: white;
        text-decoration: none;
        border-radius: 12px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        font-weight: 500;
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
    }

    .page-not-found a::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .page-not-found a:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(99, 102, 241, 0.3);
    }

    .page-not-found a:hover::before {
        opacity: 1;
    }

    .page-not-found a span {
        position: relative;
        z-index: 1;
    }

    @media (max-width: 768px) {
        .main-content {
            margin-left: 0;
            padding: 1rem;
        }

        .content-body {
            padding: 1.5rem;
            border-radius: 16px;
        }

        .page-not-found {
            padding: 3rem 1rem;
        }

        .page-not-found h2 {
            font-size: 1.5rem;
        }
    }
</style>

<main class="main-content">
    <div class="content-body">
        <?php
        // Display Messages
        if (isset($_SESSION['success_message'])) {
            echo '<div class="message message-success">
                    <i class="fas fa-check-circle"></i>
                    ' . htmlspecialchars($_SESSION['success_message']) . '
                  </div>';
            unset($_SESSION['success_message']);
        }

        if (isset($_SESSION['error_message'])) {
            echo '<div class="message message-error">
                    <i class="fas fa-exclamation-circle"></i>
                    ' . htmlspecialchars($_SESSION['error_message']) . '
                  </div>';
            unset($_SESSION['error_message']);
        }

        // Include Page Content
        switch ($page) {
            case 'tracking_report':
                include 'pages/tracking_report.php';
                break;

            case 'transactions_view':
                include 'pages/transactions_view.php';
                break;

            default:
                if (file_exists($pageFile)) {
                    include $pageFile;
                } else {
                    echo '<div class="page-not-found">
                            <i class="fas fa-exclamation-triangle"></i>
                            <h2>Page Not Found</h2>
                            <p>The requested page could not be found. Please check the URL and try again.</p>
                            <a href="index.php?page=dashboard"><i class="fas fa-home"></i> Return to Dashboard</a>
                          </div>';
                }
                break;
        }
        ?>
    </div>
</main>
