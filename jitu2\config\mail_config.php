<?php
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\Exception;

require_once __DIR__ . '/../vendor/autoload.php';

// Mail configuration settings
define('MAIL_FROM_EMAIL', '<EMAIL>');
define('MAIL_FROM_NAME', 'Astra Logistics Manager');
define('MAIL_REPLY_TO', '<EMAIL>');

// Hostinger SMTP settings
define('SMTP_HOST', 'smtp.hostinger.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', '<PERSON><PERSON><PERSON>@0505'); // Replace with your actual SMTP password
define('SMTP_ENCRYPTION', 'tls');

// Error logging
define('MAIL_ERROR_LOG', __DIR__ . '/../logs/mail_errors.log');

/**
 * Send an email using <PERSON><PERSON><PERSON>ail<PERSON>
 * 
 * @param string $to Recipient email address
 * @param string $subject Email subject
 * @param string $message Email message (HTML)
 * @param array $headers Additional headers (optional)
 * @return bool Whether the email was sent successfully
 */
function send_email($to, $subject, $message, $headers = []) {
    try {
        $mail = new PHPMailer(true);

        // Server settings
        $mail->isSMTP();
        $mail->Host = SMTP_HOST;
        $mail->SMTPAuth = true;
        $mail->Username = SMTP_USERNAME;
        $mail->Password = SMTP_PASSWORD;
        $mail->SMTPSecure = SMTP_ENCRYPTION;
        $mail->Port = SMTP_PORT;
        $mail->CharSet = 'UTF-8';

        // Recipients
        $mail->setFrom(MAIL_FROM_EMAIL, MAIL_FROM_NAME);
        $mail->addAddress($to);
        $mail->addReplyTo(MAIL_REPLY_TO);

        // Content
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $message;

        // Add custom headers
        foreach ($headers as $key => $value) {
            $mail->addCustomHeader($key, $value);
        }

        // Send email
        $result = $mail->send();
        
        // Log success
        error_log(date('Y-m-d H:i:s') . " - Email sent successfully to $to\n", 3, MAIL_ERROR_LOG);
        
        return $result;
    } catch (Exception $e) {
        // Log error
        $error_message = date('Y-m-d H:i:s') . " - Failed to send email to $to: " . $mail->ErrorInfo . "\n";
        error_log($error_message, 3, MAIL_ERROR_LOG);
        return false;
    }
}

/**
 * Format email message with HTML template
 * 
 * @param string $message Plain text message
 * @return string HTML formatted message
 */
function format_email_message($message) {
    $html = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <style>
            body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
            }
            .header {
                background: #4f46e5;
                color: white;
                padding: 20px;
                text-align: center;
                border-radius: 5px 5px 0 0;
            }
            .content {
                background: #f9fafb;
                padding: 20px;
                border: 1px solid #e5e7eb;
                border-top: none;
                border-radius: 0 0 5px 5px;
            }
            .footer {
                text-align: center;
                margin-top: 20px;
                font-size: 12px;
                color: #6b7280;
            }
            .button {
                display: inline-block;
                padding: 10px 20px;
                background: #4f46e5;
                color: white;
                text-decoration: none;
                border-radius: 5px;
                margin: 20px 0;
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>Astra Logistics Manager</h1>
        </div>
        <div class="content">
            ' . nl2br(htmlspecialchars($message)) . '
        </div>
        <div class="footer">
            <p>This is an automated message, please do not reply to this email.</p>
        </div>
    </body>
    </html>';
    
    return $html;
} 