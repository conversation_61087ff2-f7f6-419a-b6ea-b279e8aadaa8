<?php
// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Start output buffering to catch any errors
ob_start();

// Only start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Use root path for database connection
$db_path = $_SERVER['DOCUMENT_ROOT'] . '/db_connect.php';
if (!file_exists($db_path)) {
    die("Database connection file not found at: " . $db_path);
}
include $db_path;

if (!isset($_SESSION['username'])) {
    header("Location: ../login.php");
    exit();
}

// Handle file upload
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_FILES['weight_update_file'])) {
    $debug_output = [];
    $debug_output[] = "Weight update process started";
    
    try {
        // Fix the vendor autoload path
        $vendor_path = __DIR__ . '/../vendor/autoload.php';
        if (!file_exists($vendor_path)) {
            throw new Exception('Composer autoload file not found at: ' . $vendor_path);
        }
        
        require $vendor_path;
        $debug_output[] = "Autoload file included successfully";

        $username = $_SESSION['username'];
        $file = $_FILES['weight_update_file'];
        $debug_output[] = "File details: " . print_r($file, true);

        // Check if file upload was successful
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $upload_errors = [
                UPLOAD_ERR_INI_SIZE => 'The uploaded file exceeds the upload_max_filesize directive in php.ini',
                UPLOAD_ERR_FORM_SIZE => 'The uploaded file exceeds the MAX_FILE_SIZE directive in the HTML form',
                UPLOAD_ERR_PARTIAL => 'The uploaded file was only partially uploaded',
                UPLOAD_ERR_NO_FILE => 'No file was uploaded',
                UPLOAD_ERR_NO_TMP_DIR => 'Missing a temporary folder',
                UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
                UPLOAD_ERR_EXTENSION => 'A PHP extension stopped the file upload'
            ];
            $error_message = isset($upload_errors[$file['error']]) ? $upload_errors[$file['error']] : 'Unknown upload error';
            throw new Exception('File upload error: ' . $error_message);
        }

        // Validate file
        $allowed_types = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel'
        ];

        $debug_output[] = "File type: " . $file['type'];
        if (!in_array($file['type'], $allowed_types)) {
            $debug_output[] = "Invalid file type detected: " . $file['type'];
            throw new Exception('Invalid file type. Please upload an Excel file (.xlsx or .xls)');
        }

        // Create temporary table for weight updates with all required fields
        $temp_table_name = "temp_weight_updates_" . time();
        $create_temp_table = "CREATE TEMPORARY TABLE $temp_table_name (
            id INT AUTO_INCREMENT PRIMARY KEY,
            docket_no VARCHAR(50),
            new_weight DECIMAL(10,3),
            status VARCHAR(20) DEFAULT 'pending',
            error_message TEXT,
            amount DECIMAL(10,2),
            processed_at TIMESTAMP NULL,
            customer VARCHAR(50),
            mode_of_tsp VARCHAR(20),
            zone VARCHAR(50),
            pr_zone VARCHAR(50),
            ts_zone VARCHAR(50),
            rate_up_to_0250 DECIMAL(10,2),
            rate_up_to_0500 DECIMAL(10,2),
            rate_addl_500gm DECIMAL(10,2),
            rate_above_3kg DECIMAL(10,2)
        )";
        
        if (!$conn->query($create_temp_table)) {
            throw new Exception("Failed to create temporary table: " . $conn->error);
        }

        // Read Excel file
        $debug_output[] = "Attempting to load Excel file";
        try {
            $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($file['tmp_name']);
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();
            $debug_output[] = "Excel file loaded successfully. Number of rows: " . count($rows);
        } catch (Exception $e) {
            throw new Exception('Error reading Excel file: ' . $e->getMessage());
        }

        // Remove header row
        array_shift($rows);

        // First pass: Insert docket numbers and new weights into temp table
        $insert_temp_stmt = $conn->prepare("INSERT INTO $temp_table_name (docket_no, new_weight) VALUES (?, ?)");
        
        foreach ($rows as $index => $row) {
            if (empty($row[0])) {
                continue;
            }

            $docket_no = trim($row[0]);
            $new_weight = floatval(trim($row[1]));

            // Basic validation
            if ($new_weight <= 0) {
                $conn->query("UPDATE $temp_table_name SET status = 'error', error_message = 'Invalid weight value' WHERE docket_no = '$docket_no'");
                continue;
            }

            // Insert into temp table
            $insert_temp_stmt->bind_param("sd", $docket_no, $new_weight);
            $insert_temp_stmt->execute();
        }

        // Second pass: Fetch all required data from transactions and related tables
        $fetch_data_query = "
            UPDATE $temp_table_name temp
            JOIN transactions t ON temp.docket_no = t.docket_no AND t.username = '$username'
            JOIN customers c ON t.customer = c.short_name AND c.username = '$username'
            JOIN pincode_data p ON t.pincode = p.pincode
            LEFT JOIN rate_master rm ON (
                c.short_name = rm.short_name 
                AND (
                    (t.mode_of_tsp = 'Premium' AND UPPER(p.pr_zone) = UPPER(rm.pr_zone))
                    OR (t.mode_of_tsp != 'Premium' AND UPPER(p.zone) = UPPER(rm.zone))
                )
                AND rm.mode_of_tsp = CASE 
                    WHEN t.mode_of_tsp = 'Air Cargo' THEN 'Express'
                    ELSE t.mode_of_tsp 
                END
            )
            SET 
                temp.customer = t.customer,
                temp.mode_of_tsp = t.mode_of_tsp,
                temp.zone = p.zone,
                temp.pr_zone = p.pr_zone,
                temp.ts_zone = p.ts_zone,
                temp.rate_up_to_0250 = rm.up_to_0250,
                temp.rate_up_to_0500 = rm.up_to_0500,
                temp.rate_addl_500gm = rm.addl_500gm,
                temp.rate_above_3kg = rm.above_3kg,
                temp.status = CASE 
                    WHEN rm.short_name IS NULL THEN 'error'
                    ELSE 'pending'
                END,
                temp.error_message = CASE 
                    WHEN rm.short_name IS NULL THEN CONCAT('Rate not found for customer ''', t.customer, ''' and mode ''', t.mode_of_tsp, '''')
                    ELSE NULL
                END
            WHERE temp.status = 'pending'
        ";

        if (!$conn->query($fetch_data_query)) {
            throw new Exception("Failed to fetch required data: " . $conn->error);
        }

        // Third pass: Calculate amounts for valid records
        $calculate_amount_query = "
            UPDATE $temp_table_name
            SET 
                amount = CASE
                    WHEN mode_of_tsp = 'Surface' THEN
                        CASE
                            WHEN CEIL(GREATEST(new_weight, rate_above_3kg)) <= 10 THEN
                                CEIL(GREATEST(new_weight, rate_above_3kg)) * rate_up_to_0250
                            WHEN CEIL(GREATEST(new_weight, rate_above_3kg)) <= 20 THEN
                                CEIL(GREATEST(new_weight, rate_above_3kg)) * rate_up_to_0500
                            ELSE
                                CEIL(GREATEST(new_weight, rate_above_3kg)) * rate_addl_500gm
                        END
                    WHEN mode_of_tsp = 'Premium' THEN
                        CASE
                            WHEN new_weight <= 0.250 THEN
                                rate_up_to_0250
                            WHEN new_weight <= 10 THEN
                                CASE
                                    WHEN CEIL(new_weight * 2) / 2 <= 0.500 THEN
                                        rate_up_to_0500
                                    ELSE
                                        rate_up_to_0500 + ((CEIL(new_weight * 2) / 2 - 0.500) / 0.500) * rate_addl_500gm
                                END
                            ELSE
                                CEIL(new_weight) * rate_above_3kg
                        END
                    ELSE -- Express mode
                        CASE
                            WHEN new_weight <= 0.250 THEN
                                rate_up_to_0250
                            WHEN CEIL(new_weight * 2) / 2 <= 0.500 THEN
                                rate_up_to_0500
                            ELSE
                                rate_up_to_0500 + ((CEIL(new_weight * 2) / 2 - 0.500) / 0.500) * rate_addl_500gm
                        END
                END,
                status = 'calculated'
            WHERE status = 'pending'
        ";

        if (!$conn->query($calculate_amount_query)) {
            throw new Exception("Failed to calculate amounts: " . $conn->error);
        }

        // Fourth pass: Update transactions table
        $update_transactions_query = "
            UPDATE transactions t
            JOIN $temp_table_name temp ON t.docket_no = temp.docket_no
            SET 
                t.weight = temp.new_weight,
                t.amount = CASE 
                    WHEN t.entry_type = 'credit' THEN temp.amount
                    ELSE t.amount  -- Keep original amount for cash entries
                END
            WHERE t.username = '$username' 
            AND temp.status = 'calculated'
        ";

        if (!$conn->query($update_transactions_query)) {
            throw new Exception("Failed to update transactions: " . $conn->error);
        }

        // Get final results
        $results = $conn->query("SELECT * FROM $temp_table_name ORDER BY id");
        $processed_records = [];
        while ($row = $results->fetch_assoc()) {
            $processed_records[] = $row;
        }

        // Calculate statistics
        $stats = $conn->query("
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'calculated' THEN 1 ELSE 0 END) as success_count,
                SUM(CASE WHEN status = 'error' THEN 1 ELSE 0 END) as error_count
            FROM $temp_table_name
        ")->fetch_assoc();

        // Store results in session
        $_SESSION['weight_update_summary'] = [
            'total_rows' => $stats['total'],
            'success_count' => $stats['success_count'],
            'error_count' => $stats['error_count'],
            'processed_records' => $processed_records
        ];

        if ($stats['success_count'] > 0) {
            $_SESSION['upload_message'] = "Successfully updated {$stats['success_count']} records!";
        }
        if ($stats['error_count'] > 0) {
            $_SESSION['upload_errors'] = array_column(
                array_filter($processed_records, function($r) { return $r['status'] === 'error'; }),
                'error_message'
            );
        }

        // Clean up
        $conn->query("DROP TEMPORARY TABLE IF EXISTS $temp_table_name");

    } catch (Exception $e) {
        $debug_output[] = "Critical error in weight update process: " . $e->getMessage();
        $_SESSION['upload_error'] = $e->getMessage();
    }

    // Store debug output in session
    $_SESSION['debug_output'] = $debug_output;
    
    // Get any output that might have been generated
    $output = ob_get_clean();
    if (!empty($output)) {
        $_SESSION['debug_output'][] = "Additional output: " . $output;
    }
    
    // Redirect back to upload page
    header("Location: ../index.php?page=upload");
    exit();
}
?> 