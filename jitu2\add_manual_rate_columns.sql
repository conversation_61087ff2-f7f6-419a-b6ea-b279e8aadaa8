-- Add columns for manual rate entry functionality
-- Execute this SQL in phpMyAdmin or your database management tool

-- Add entry_without_quote column to transactions table
ALTER TABLE `transactions` 
ADD COLUMN `entry_without_quote` TINYINT(1) DEFAULT 0 COMMENT 'Flag to indicate if entry was made without quote calculation';

-- Add manual_rate column to transactions table  
ALTER TABLE `transactions` 
ADD COLUMN `manual_rate` DECIMAL(10,2) DEFAULT 0.00 COMMENT 'Manual rate entered when entry_without_quote is enabled';

-- Add index for better performance on entry_without_quote queries
CREATE INDEX `idx_entry_without_quote` ON `transactions` (`entry_without_quote`);

-- Verify the columns were added successfully
DESCRIBE `transactions`;
