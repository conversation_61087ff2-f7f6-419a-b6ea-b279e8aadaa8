<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
session_start();
include '../db_connect.php';

if (!isset($_SESSION['username'])) {
    die('unauthorized');
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['docket_no'])) {
    $docket_no = trim($_POST['docket_no']);
    $username = $_SESSION['username'];

    // Check if C-Note validation is enabled for this user
    $settings_stmt = $conn->prepare("SELECT cnote_validation_enabled FROM settings WHERE username = ?");
    $settings_stmt->bind_param("s", $username);
    $settings_stmt->execute();
    $settings_result = $settings_stmt->get_result();
    $settings_data = $settings_result->fetch_assoc();
    $cnote_validation_enabled = $settings_data['cnote_validation_enabled'] ?? 1; // Default to enabled
    $settings_stmt->close();

    if ($cnote_validation_enabled) {
        // Check if docket exists in cn_entries
        $stmt = $conn->prepare("SELECT cn_number FROM cn_entries WHERE cn_number = ?");
        $stmt->bind_param("s", $docket_no);
        $stmt->execute();
        $stmt->store_result();

        if ($stmt->num_rows > 0) {
            echo 'valid';
        } else {
            echo 'invalid';
        }

        $stmt->close();
    } else {
        // Validation is disabled, always return valid
        echo 'valid';
    }

    $conn->close();
}
?>