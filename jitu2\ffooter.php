<style>
    .footer {
        height: auto;
        min-height: 60px;
        background: var(--glass-bg);
        backdrop-filter: blur(12px);
        -webkit-backdrop-filter: blur(12px);
        border-top: 1px solid var(--glass-border);
        color: var(--text-color);
        padding: 1rem 2rem;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.05);
        z-index: 1000;
    }

    .footer::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, 
            var(--primary-color) 0%,
            var(--secondary-color) 25%,
            var(--accent-color) 50%,
            var(--success-color) 75%,
            var(--warning-color) 100%
        );
        opacity: 0.8;
    }

    .footer-content {
        max-width: 1200px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 2rem;
    }

    .footer-brand {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .footer-brand .logo {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        transition: all 0.3s ease;
    }

    .footer-brand .logo:hover {
        transform: translateY(-2px);
    }

    .footer-brand i {
        font-size: 1.4rem;
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        filter: drop-shadow(0 2px 4px rgba(79, 70, 229, 0.2));
        transition: all 0.3s ease;
    }

    .footer-brand .logo:hover i {
        transform: scale(1.1) rotate(-5deg);
    }

    .footer-brand .brand-text {
        display: flex;
        flex-direction: column;
        line-height: 1.2;
    }

    .footer-brand .brand-name {
        font-size: 1.1rem;
        font-weight: 700;
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        letter-spacing: -0.01em;
    }

    .footer-brand .brand-tagline {
        font-size: 0.7rem;
        color: var(--text-muted);
        font-weight: 500;
        letter-spacing: 0.03em;
        text-transform: uppercase;
        margin-top: 0.1rem;
    }

    .copyright-text {
        font-size: 0.8rem;
        color: var(--text-muted);
        margin: 0;
        white-space: nowrap;
    }

    .footer-links {
        display: flex;
        gap: 2rem;
        align-items: center;
    }

    .footer-section {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .footer-section a {
        color: var(--text-muted);
        text-decoration: none;
        transition: all 0.3s ease;
        font-size: 0.8rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem;
        border-radius: 6px;
    }

    .footer-section a:hover {
        color: var(--primary-color);
        background: var(--hover-bg);
    }

    .footer-section a i {
        font-size: 0.9rem;
        opacity: 0.8;
    }

    .footer-bottom {
        margin-top: 0.5rem;
        padding-top: 0.5rem;
        border-top: 1px solid var(--glass-border);
        text-align: center;
        color: var(--text-muted);
        font-size: 0.75rem;
    }

    /* Add padding to main content to prevent footer overlap */
    .main-content {
        padding-bottom: calc(var(--header-height) + 1rem);
    }

    @media (max-width: 768px) {
        .footer {
            padding: 1rem;
        }

        .footer-content {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
        }

        .footer-links {
            flex-direction: column;
            gap: 0.75rem;
        }

        .footer-section {
            width: 100%;
            justify-content: center;
        }

        /* Adjust main content padding for mobile */
        .main-content {
            padding-bottom: calc(var(--header-height) + 2rem);
        }
    }
</style>

<footer class="footer">
    <div class="footer-content">
        <p class="copyright-text"><strong>&copy; <?php echo date('Y'); ?> Yash Enterprises. All rights reserved.</strong></p>
        <p style="margin-top: 10px; font-size: 0.9rem; opacity: 0.8;">
            <a href="tel:+917208521590" style="color: #4f46e5; text-decoration: none; margin-right: 15px;">
                <i class="fas fa-phone" style="margin-right: 5px;"></i>
                +91 7208521590
            </a>
            | Powered by <strong>Astra Digital Solutions</strong>
        </p>

        <div class="footer-links">
            <div class="footer-section">
                <a href="mailto:<EMAIL>">
                    <i class="fas fa-envelope"></i>
                    <span><EMAIL></span>
                </a>
            </div>
            <div class="footer-section">
                <a href="tel:+917208521590">
                    <i class="fas fa-phone"></i>
                    <span>+91 7208521590</span>
                </a>
            </div>
        </div>
    </div>
</footer>
</body>
</html>