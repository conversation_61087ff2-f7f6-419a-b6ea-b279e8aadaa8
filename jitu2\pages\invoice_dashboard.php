<?php
session_start();
include 'db_connect.php';

if (!isset($_SESSION['username'])) {
    die("<script>alert('You must be logged in!'); window.location.href='../login.php';</script>");
}

$username = $_SESSION['username'];

// Fetch customers
$query = "SELECT short_name FROM customers WHERE username = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("s", $username);
$stmt->execute();
$result = $stmt->get_result();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generate Invoice</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        :root {
            --primary-blue: #2196F3;
            --light-blue: #E3F2FD;
            --hover-blue: #1976D2;
            --text-dark: #2c3e50;
            --border-color: #e0e0e0;
            --background: #F8FAFC;
            --success-green: #28a745;
            --hover-green: #218838;
        }

        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            background: var(--background);
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            margin-left: 0px;
            margin-top: 0px;
        }

        h2 {
            color: var(--primary-blue);
            font-size: 1.75rem;
            font-weight: 600;
            margin: 0 0 2rem 0;
        }

        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        th {
            background: var(--primary-blue);
            color: white;
            padding: 1rem;
            font-weight: 500;
            text-align: left;
            font-size: 0.95rem;
            white-space: nowrap;
        }

        td {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-dark);
            font-size: 0.95rem;
        }

        tr:last-child td {
            border-bottom: none;
        }

        tr:hover td {
            background: var(--background);
        }

        input[type="date"] {
            padding: 0.5rem;
            border: 2px solid var(--border-color);
            border-radius: 6px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }

        input[type="date"]:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
        }

        button {
            background: var(--success-green);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        button:hover {
            background: var(--hover-green);
            transform: translateY(-1px);
        }

        button i {
            font-size: 1rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
                margin: 1rem;
            }

            table {
                display: block;
                overflow-x: auto;
            }

            th, td {
                white-space: nowrap;
            }
        }
    </style>
</head>
<body>

<div class="container">
    <h2><i class="fas fa-file-invoice"></i> Generate Invoice</h2>

    <table>
        <thead>
        <tr>
            <th>Customer</th>
            <th>From Date</th>
            <th>To Date</th>
            <th>Action</th>
        </tr>
        </thead>
        <tbody>
        <?php while ($row = $result->fetch_assoc()) { ?>
            <tr>
                <form action="index.php" method="GET">
                    <input type="hidden" name="page" value="generate_invoice">
                    <input type="hidden" name="customer" value="<?php echo $row['short_name']; ?>">
                    <td><?php echo $row['short_name']; ?></td>
                    <td><input type="date" name="from_date" required></td>
                    <td><input type="date" name="to_date" required></td>
                    <td>
                        <button type="submit">
                            <i class="fas fa-file-invoice"></i>
                            Generate Invoice
                        </button>
                    </td>
                </form>
            </tr>
        <?php } ?>
        </tbody>
    </table>
</div>

</body>
</html>
