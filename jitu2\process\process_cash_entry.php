<?php
session_start();
include '../db_connect.php'; // Move up one directory to access db_connect.php

// Add debugging output to browser console via JavaScript
function console_log($message, $data = null) {
    $json_data = $data ? json_encode($data) : 'null';
    echo "<script>console.log('🐛 PHP DEBUG: " . addslashes($message) . "', " . $json_data . ");</script>";
}

if (!isset($_SESSION['username'])) {
    die("<script>alert('Error: You must be logged in.'); window.location.href='login.php';</script>");
}

$username = $_SESSION['username']; // Get the logged-in user

// Debug: Log initial processing start
echo "<script>
console.log('🚀 PROCESS_CASH_ENTRY.PHP START');
console.log('👤 Session Username:', '" . addslashes($username) . "');
console.log('📥 POST Data Received:', " . json_encode($_POST) . ");
console.log('🔍 Confirm Duplicate Flag:', '" . (isset($_POST['confirm_duplicate']) ? $_POST['confirm_duplicate'] : 'NOT SET') . "');
</script>";
$docket_no = $_POST['docket_no'];
$docket_date = $_POST['docket_date'];

// Validate and standardize date format
try {
    $date = new DateTime($docket_date);
    $docket_date = $date->format('Y-m-d');
} catch (Exception $e) {
    die("<script>alert('❌ Error: Invalid date format. Please use YYYY-MM-DD format.'); window.history.back();</script>");
}

$pincode = $_POST['pincode'];
$destination = $_POST['destination'];
$weight = $_POST['weight'];
$mode_of_tsp = $_POST['mode_of_tsp'];
$amount = $_POST['amount'];
$payment_status = $_POST['payment_status'];
$remarks = $_POST['remarks'];
$mobile1 = $_POST['mobile1'];
$mobile2 = $_POST['mobile2'];

// Set payment_received_date based on payment status
$payment_received_date = null;
if ($payment_status === 'Cash-Received' || $payment_status === 'Online-Received') {
    $payment_received_date = date('Y-m-d');
}

// ✅ Step 1: Check if C-Note validation is enabled, then validate docket_no
$settings_stmt = $conn->prepare("SELECT cnote_validation_enabled FROM settings WHERE username = ?");
$settings_stmt->bind_param("s", $username);
$settings_stmt->execute();
$settings_result = $settings_stmt->get_result();
$settings_data = $settings_result->fetch_assoc();
$cnote_validation_enabled = $settings_data['cnote_validation_enabled'] ?? 1; // Default to enabled
$settings_stmt->close();

if ($cnote_validation_enabled) {
    $validate_stmt = $conn->prepare("SELECT cn_number FROM cn_entries WHERE cn_number = ? AND username = ?");
    $validate_stmt->bind_param("ss", $docket_no, $username);
    $validate_stmt->execute();
    $validate_stmt->store_result();

    if ($validate_stmt->num_rows === 0) {
        die("<script>alert('❌ Error: Not a valid Docket Number for this user!'); window.history.back();</script>");
    }
    $validate_stmt->close();
}

// ✅ Step 2: Check if docket_no already exists in transactions for this user
$check_stmt = $conn->prepare("SELECT docket_no, docket_date, created_at, entry_type FROM transactions WHERE docket_no = ? AND username = ? ORDER BY created_at DESC LIMIT 1");
$check_stmt->bind_param("ss", $docket_no, $username);
$check_stmt->execute();
$check_result = $check_stmt->get_result();
$existing_entry = $check_result->fetch_assoc();
$check_stmt->close();

// Check if user has confirmed duplicate entry
$confirm_duplicate = isset($_POST['confirm_duplicate']) && $_POST['confirm_duplicate'] == '1';

// Format the existing entry date information
$existing_date_info = '';
$existing_entry_type = '';
if ($existing_entry) {
    $existing_entry_type = ucfirst($existing_entry['entry_type']);
    $docket_date = date('d-M-Y', strtotime($existing_entry['docket_date']));
    $created_date = date('d-M-Y H:i', strtotime($existing_entry['created_at']));
    $existing_date_info = "Originally used on: $docket_date (Entry created: $created_date)";
}

echo "<script>
console.log('🔍 DUPLICATE CHECK RESULTS:');
console.log('  Duplicate entry found:', " . ($existing_entry ? 'true' : 'false') . ");
console.log('  Existing entry type:', '" . addslashes($existing_entry_type) . "');
console.log('  Existing docket date:', '" . addslashes($existing_entry ? $existing_entry['docket_date'] : 'N/A') . "');
console.log('  Existing created date:', '" . addslashes($existing_entry ? $existing_entry['created_at'] : 'N/A') . "');
console.log('  Confirm duplicate flag:', " . ($confirm_duplicate ? 'true' : 'false') . ");
console.log('  Will show confirmation:', " . (($existing_entry && !$confirm_duplicate) ? 'true' : 'false') . ");
</script>";

if ($existing_entry && !$confirm_duplicate) {
    // Duplicate exists and user hasn't confirmed - ask for confirmation
    echo "<script>console.log('⚠️ SHOWING DUPLICATE CONFIRMATION DIALOG');</script>";
    $conn->close();

    // Create form data to resubmit with confirmation
    $form_data = '';
    foreach ($_POST as $key => $value) {
        if ($key !== 'confirm_duplicate') {
            $form_data .= '<input type="hidden" name="' . htmlspecialchars($key) . '" value="' . htmlspecialchars($value) . '">';
        }
    }

    echo '
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Duplicate Entry Confirmation</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
                background-color: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 100vh;
                padding: 20px;
            }

            .modal {
                background: white;
                border-radius: 12px;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                max-width: 500px;
                width: 100%;
                animation: modalSlideIn 0.3s ease-out;
                overflow: hidden;
            }

            @keyframes modalSlideIn {
                from {
                    opacity: 0;
                    transform: translateY(-50px) scale(0.9);
                }
                to {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
            }

            .modal-header {
                background: linear-gradient(135deg, #ff6b6b, #ee5a24);
                color: white;
                padding: 24px;
                text-align: center;
            }

            .modal-icon {
                font-size: 48px;
                margin-bottom: 12px;
                display: block;
            }

            .modal-title {
                font-size: 24px;
                font-weight: 600;
                margin-bottom: 8px;
            }

            .modal-subtitle {
                font-size: 16px;
                opacity: 0.9;
            }

            .modal-body {
                padding: 32px 24px;
                text-align: center;
            }

            .warning-message {
                font-size: 18px;
                color: #2c3e50;
                line-height: 1.6;
                margin-bottom: 8px;
            }

            .docket-highlight {
                font-weight: 700;
                color: #e74c3c;
                background: #ffeaa7;
                padding: 4px 8px;
                border-radius: 6px;
                display: inline-block;
                margin: 0 4px;
            }

            .sub-message {
                font-size: 16px;
                color: #7f8c8d;
                margin-top: 16px;
            }

            .existing-entry-info {
                background: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                padding: 16px;
                margin: 20px 0;
                text-align: left;
            }

            .entry-type-badge {
                display: inline-block;
                background: linear-gradient(135deg, #ff6b6b, #ee5a24);
                color: white;
                padding: 4px 10px;
                border-radius: 12px;
                font-size: 11px;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                margin-bottom: 12px;
            }

            .date-info {
                font-size: 14px;
                color: #495057;
                line-height: 1.6;
                margin: 0;
            }

            .date-info strong {
                color: #343a40;
                font-weight: 600;
            }

            .modal-footer {
                padding: 24px;
                background: #f8f9fa;
                display: flex;
                gap: 12px;
                justify-content: center;
            }

            .btn {
                padding: 12px 32px;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.2s ease;
                min-width: 120px;
            }

            .btn-confirm {
                background: linear-gradient(135deg, #00b894, #00a085);
                color: white;
            }

            .btn-confirm:hover {
                background: linear-gradient(135deg, #00a085, #008f75);
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(0, 184, 148, 0.3);
            }

            .btn-cancel {
                background: #ddd;
                color: #666;
            }

            .btn-cancel:hover {
                background: #ccc;
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            }

            .loading {
                display: none;
                text-align: center;
                padding: 20px;
            }

            .spinner {
                border: 3px solid #f3f3f3;
                border-top: 3px solid #00b894;
                border-radius: 50%;
                width: 30px;
                height: 30px;
                animation: spin 1s linear infinite;
                margin: 0 auto 12px;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        </style>
    </head>
    <body>
        <div class="modal">
            <div class="modal-header">
                <span class="modal-icon">⚠️</span>
                <h2 class="modal-title">Duplicate Entry Detected</h2>
                <p class="modal-subtitle">This docket number already exists</p>
            </div>

            <div class="modal-body">
                <p class="warning-message">
                    Docket No. <span class="docket-highlight">' . htmlspecialchars($docket_no) . '</span> already exists for this user.
                </p>
                <div class="existing-entry-info">
                    <div class="entry-type-badge">' . htmlspecialchars($existing_entry_type) . ' Entry</div>
                    <p class="date-info">
                        <strong>📅 Docket Date:</strong> ' . htmlspecialchars($docket_date) . '<br>
                        <strong>🕒 Entry Created:</strong> ' . htmlspecialchars($created_date) . '
                    </p>
                </div>
                <p class="sub-message">
                    Do you want to add this duplicate entry anyway?
                </p>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-cancel" onclick="cancelDuplicate()">
                    ❌ Cancel
                </button>
                <button type="button" class="btn btn-confirm" onclick="confirmDuplicate()">
                    ✅ Yes, Add Duplicate
                </button>
            </div>

            <div class="loading" id="loadingDiv">
                <div class="spinner"></div>
                <p>Processing your request...</p>
            </div>
        </div>

        <form id="confirmForm" method="POST" action="process_cash_entry.php" style="display:none;">
            ' . $form_data . '
            <input type="hidden" name="confirm_duplicate" value="1">
        </form>

        <script>
            // Detailed console debugging
            console.log("🔍 MODAL DUPLICATE CONFIRMATION DEBUG START");
            console.log("📋 Docket Number:", "' . htmlspecialchars($docket_no) . '");
            console.log("👤 Username:", "' . htmlspecialchars($username) . '");
            console.log("📝 Form Data Length:", ' . strlen($form_data) . ');
            console.log("🔗 Action URL:", "process_cash_entry.php");

            function confirmDuplicate() {
                console.log("✅ User confirmed duplicate entry");

                const confirmForm = document.getElementById("confirmForm");
                const loadingDiv = document.getElementById("loadingDiv");
                const modalFooter = document.querySelector(".modal-footer");
                const modalBody = document.querySelector(".modal-body");

                if (!confirmForm) {
                    console.error("❌ ERROR: confirmForm element not found!");
                    alert("Error: Form not found. Please try again.");
                    window.history.back();
                    return;
                }

                // Show loading state
                modalFooter.style.display = "none";
                modalBody.style.display = "none";
                loadingDiv.style.display = "block";

                console.log("📤 Submitting form with duplicate confirmation");
                console.log("🔢 Form elements count:", confirmForm.elements.length);

                // Log all form elements being submitted
                for (let i = 0; i < confirmForm.elements.length; i++) {
                    const element = confirmForm.elements[i];
                    console.log("  Field:", element.name, "=", element.value);
                }

                try {
                    confirmForm.submit();
                    console.log("📨 Form submitted successfully");
                } catch (error) {
                    console.error("❌ Form submission error:", error);
                    alert("Error submitting form: " + error.message);
                    // Restore modal state on error
                    modalFooter.style.display = "flex";
                    modalBody.style.display = "block";
                    loadingDiv.style.display = "none";
                }
            }

            function cancelDuplicate() {
                console.log("❌ User cancelled duplicate entry");
                window.history.back();
            }

            // Additional error handling
            window.addEventListener("error", function(e) {
                console.error("🚨 JavaScript Error:", e.error);
                console.error("📍 Error location:", e.filename + ":" + e.lineno);
            });

            // Auto-focus on confirm button for better UX
            document.addEventListener("DOMContentLoaded", function() {
                console.log("🚀 Modal loaded and ready");
                document.querySelector(".btn-confirm").focus();
            });

            // Allow Enter key to confirm, Escape to cancel
            document.addEventListener("keydown", function(e) {
                if (e.key === "Enter") {
                    confirmDuplicate();
                } else if (e.key === "Escape") {
                    cancelDuplicate();
                }
            });

            console.log("🔍 MODAL DUPLICATE CONFIRMATION DEBUG END");
        </script>
    </body>
    </html>
    ';
    exit;
}

echo "<script>
console.log('✅ PROCEEDING TO DATABASE INSERTION');
console.log('📋 Final Data to Insert:');
console.log('  docket_no:', '" . addslashes($docket_no) . "');
console.log('  docket_date:', '" . addslashes($docket_date) . "');
console.log('  pincode:', '" . addslashes($pincode) . "');
console.log('  destination:', '" . addslashes($destination) . "');
console.log('  weight:', '" . addslashes($weight) . "');
console.log('  mode_of_tsp:', '" . addslashes($mode_of_tsp) . "');
console.log('  amount:', '" . addslashes($amount) . "');
console.log('  payment_status:', '" . addslashes($payment_status) . "');
console.log('  payment_received_date:', '" . addslashes($payment_received_date) . "');
console.log('  username:', '" . addslashes($username) . "');
</script>";

// ✅ Step 3: Insert into database
$stmt = $conn->prepare("
    INSERT INTO transactions
    (id, entry_type, customer, docket_no, docket_date, pincode, destination, weight, mode_of_tsp, waybill_value, remarks, amount, entry_ts, waybill_percent, oda_chrg, owner_risk, carrier_risk, payment_status, payment_received_date, waybill, mobile1, mobile2, username, created_at)
    VALUES (NULL, 'cash', NULL, ?, ?, ?, ?, ?, ?, NULL, ?, ?, NULL, 0.00, 0.00, 0.00, 0.00, ?, ?, NULL, ?, ?, ?, CURRENT_TIMESTAMP)
");
$stmt->bind_param("sssssssssssss", $docket_no, $docket_date, $pincode, $destination, $weight, $mode_of_tsp, $remarks, $amount, $payment_status, $payment_received_date, $mobile1, $mobile2, $username);

echo "<script>console.log('💾 Executing database insertion...');</script>";

// Add more detailed error checking
if ($stmt === false) {
    echo "<script>console.error('❌ Prepare statement failed:', '" . addslashes($conn->error) . "');</script>";
    die("<script>alert('Database prepare error: " . addslashes($conn->error) . "'); window.history.back();</script>");
}

$result = $stmt->execute();

if ($result) {
    echo "<script>console.log('✅ Database insertion successful! Inserted ID: " . $conn->insert_id . "');</script>";
} else {
    echo "<script>console.error('❌ Database insertion failed:', '" . addslashes($stmt->error) . "');</script>";
    echo "<script>console.error('❌ MySQL Error Code:', " . $stmt->errno . ");</script>";

    // Check if it's a duplicate key error (Error code 1062)
    if ($stmt->errno == 1062) {
        echo "<script>console.error('🚫 UNIQUE CONSTRAINT VIOLATION: The database has a UNIQUE constraint on (docket_no, username) that prevents duplicate entries.');</script>";
        die("<script>
            alert('❌ Database Constraint Error:\\n\\nThe database has a UNIQUE constraint that prevents duplicate docket numbers for the same user.\\n\\nTo allow duplicate entries, the database constraint needs to be removed.\\n\\nError: " . addslashes($stmt->error) . "');
            window.history.back();
        </script>");
    } else {
        die("<script>alert('Database insertion error: " . addslashes($stmt->error) . "'); window.history.back();</script>");
    }
}

$stmt->close();
$conn->close();

// ✅ Success Message
echo "<script>
console.log('🎉 PROCESS COMPLETED SUCCESSFULLY');
alert('✅ Cash Entry successfully saved!');
window.location.href = 'https://astradigitalsolutions.in/index.php?page=cash-entry';
</script>";
?>
