<?php
session_start();
include 'db_connect.php';

if (!isset($_SESSION['username'])) {
    header('Location: ../login.php');
    exit();
}

$username = $_SESSION['username'];

// Fetch user data
$stmt = $conn->prepare("SELECT * FROM users WHERE username = ?");
$stmt->bind_param("s", $username);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();

// Get success message if exists
$success_message = '';
if (isset($_SESSION['profile_message'])) {
    $success_message = $_SESSION['profile_message'];
    unset($_SESSION['profile_message']);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Profile</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .profile-container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .form-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .form-group input:focus {
            border-color: #4CAF50;
            outline: none;
        }
        .submit-btn {
            grid-column: 1 / -1;
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .submit-btn:hover {
            background: #45a049;
        }
        .section-title {
            grid-column: 1 / -1;
            margin-top: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #4CAF50;
            color: #333;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .alert-danger {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 4px;
            background-color: #4CAF50;
            color: white;
            font-size: 16px;
            z-index: 1000;
            display: none;
            animation: slideIn 0.5s ease-out;
        }
        .preview-container {
            width: 150px;
            height: 150px;
            border: 2px dashed #ddd;
            border-radius: 4px;
            margin-top: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        .preview-container img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        .show-message {
            display: block;
        }
    </style>
</head>
<body>

<?php if ($success_message): ?>
    <div class="message" id="successMessage">
        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const message = document.getElementById('successMessage');
            message.classList.add('show-message');
            setTimeout(() => {
                message.classList.remove('show-message');
            }, 3000);
        });
    </script>
<?php endif; ?>

<div class="profile-container">
    <h2><i class="fas fa-user-circle"></i> User Profile</h2>
    
    <?php
    if (isset($_SESSION['profile_message'])) {
        $class = strpos($_SESSION['profile_message'], 'success') !== false ? 'alert-success' : 'alert-danger';
        echo "<div class='alert {$class}'>{$_SESSION['profile_message']}</div>";
        unset($_SESSION['profile_message']);
    }
    ?>

    <form action="pages/update_profile.php" method="POST" enctype="multipart/form-data">
        <div class="form-grid">
            <h3 class="section-title">Personal Information</h3>
            
            <div class="form-group">
                <label for="owner_name">Owner Name</label>
                <input type="text" id="owner_name" name="owner_name" value="<?php echo htmlspecialchars($user['owner_name'] ?? ''); ?>">
            </div>

            <div class="form-group">
                <label for="profile_picture">Profile Picture</label>
                <input type="file" id="profile_picture" name="profile_picture" accept="image/*" onchange="previewImage(this, 'profile-preview')">
                <div class="preview-container">
                    <?php if (!empty($user['profile_picture'])): ?>
                        <img id="profile-preview" src="<?php echo htmlspecialchars($user['profile_picture']); ?>" alt="Profile Picture">
                    <?php else: ?>
                        <img id="profile-preview" src="#" alt="Profile Picture Preview" style="display: none;">
                    <?php endif; ?>
                </div>
            </div>

            <h3 class="section-title">Business Information</h3>
            
            <div class="form-group">
                <label for="franchisee_name">Franchisee Name</label>
                <input type="text" id="franchisee_name" name="franchisee_name" value="<?php echo htmlspecialchars($user['franchisee_name'] ?? ''); ?>">
            </div>

            <div class="form-group">
                <label for="company_logo">Company Logo</label>
                <input type="file" id="company_logo" name="company_logo" accept="image/*" onchange="previewImage(this, 'logo-preview')">
                <div class="preview-container">
                    <?php if (!empty($user['company_logo'])): ?>
                        <img id="logo-preview" src="<?php echo htmlspecialchars($user['company_logo']); ?>" alt="Company Logo">
                    <?php else: ?>
                        <img id="logo-preview" src="#" alt="Company Logo Preview" style="display: none;">
                    <?php endif; ?>
                </div>
            </div>

            <div class="form-group">
                <label for="address_line1">Address Line 1</label>
                <input type="text" id="address_line1" name="address_line1" value="<?php echo htmlspecialchars($user['address_line1'] ?? ''); ?>">
            </div>

            <div class="form-group">
                <label for="address_line2">Address Line 2</label>
                <input type="text" id="address_line2" name="address_line2" value="<?php echo htmlspecialchars($user['address_line2'] ?? ''); ?>">
            </div>

            <div class="form-group">
                <label for="address_line3">Address Line 3</label>
                <input type="text" id="address_line3" name="address_line3" value="<?php echo htmlspecialchars($user['address_line3'] ?? ''); ?>">
            </div>

            <div class="form-group">
                <label for="city">City</label>
                <input type="text" id="city" name="city" value="<?php echo htmlspecialchars($user['city'] ?? ''); ?>">
            </div>

            <div class="form-group">
                <label for="pincode">Pincode</label>
                <input type="text" id="pincode" name="pincode" value="<?php echo htmlspecialchars($user['pincode'] ?? ''); ?>">
            </div>

            <div class="form-group">
                <label for="zone">Zone</label>
                <input type="text" id="zone" name="zone" value="<?php echo htmlspecialchars($user['zone'] ?? ''); ?>">
            </div>

            <div class="form-group">
                <label for="phone">Phone No.</label>
                <input type="text" id="phone" name="phone" value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
            </div>

            <div class="form-group">
                <label for="mobile">Mobile No.</label>
                <input type="text" id="mobile" name="mobile" value="<?php echo htmlspecialchars($user['mobile'] ?? ''); ?>">
            </div>

            <div class="form-group">
                <label for="business_email">Business Email ID</label>
                <input type="email" id="business_email" name="business_email" value="<?php echo htmlspecialchars($user['business_email'] ?? ''); ?>">
            </div>

            <h3 class="section-title">Tax Information</h3>

            <div class="form-group">
                <label for="gst_no">GST No.</label>
                <input type="text" id="gst_no" name="gst_no" value="<?php echo htmlspecialchars($user['gst_no'] ?? ''); ?>">
            </div>

            <div class="form-group">
                <label for="pan_no">PAN No.</label>
                <input type="text" id="pan_no" name="pan_no" value="<?php echo htmlspecialchars($user['pan_no'] ?? ''); ?>">
            </div>

            <div class="form-group">
                <label for="state_code">State Code</label>
                <input type="text" id="state_code" name="state_code" value="<?php echo htmlspecialchars($user['state_code'] ?? ''); ?>">
            </div>

            <div class="form-group">
                <label for="sac_code">SAC Code</label>
                <input type="text" id="sac_code" name="sac_code" value="<?php echo htmlspecialchars($user['sac_code'] ?? ''); ?>">
            </div>

            <h3 class="section-title">Bank Information</h3>

            <div class="form-group">
                <label for="account_name">Account Name</label>
                <input type="text" id="account_name" name="account_name" value="<?php echo htmlspecialchars($user['account_name'] ?? ''); ?>">
            </div>

            <div class="form-group">
                <label for="bank_name">Bank Name</label>
                <input type="text" id="bank_name" name="bank_name" value="<?php echo htmlspecialchars($user['bank_name'] ?? ''); ?>">
            </div>

            <div class="form-group">
                <label for="branch">Branch</label>
                <input type="text" id="branch" name="branch" value="<?php echo htmlspecialchars($user['branch'] ?? ''); ?>">
            </div>

            <div class="form-group">
                <label for="account_no">Account No.</label>
                <input type="text" id="account_no" name="account_no" value="<?php echo htmlspecialchars($user['account_no'] ?? ''); ?>">
            </div>

            <div class="form-group">
                <label for="account_type">Account Type</label>
                <input type="text" id="account_type" name="account_type" value="<?php echo htmlspecialchars($user['account_type'] ?? ''); ?>">
            </div>

            <div class="form-group">
                <label for="ifsc_code">IFSC Code</label>
                <input type="text" id="ifsc_code" name="ifsc_code" value="<?php echo htmlspecialchars($user['ifsc_code'] ?? ''); ?>">
            </div>

            <div class="form-group">
                <label for="website">Website</label>
                <input type="url" id="website" name="website" value="<?php echo htmlspecialchars($user['website'] ?? ''); ?>">
            </div>

            <button type="submit" class="submit-btn">Update Profile</button>
        </div>
    </form>
</div>

<script>
function previewImage(input, previewId) {
    const preview = document.getElementById(previewId);
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.src = e.target.result;
            preview.style.display = 'block';
        }
        reader.readAsDataURL(input.files[0]);
    }
}
</script>

</body>
</html> 