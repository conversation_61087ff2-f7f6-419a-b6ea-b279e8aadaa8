<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session and include required files
session_start();
include '../db_connect.php';

// Remove the require since we're not using it
// require_once 'calculate_surface_rate.php';

if (!isset($_SESSION['username'])) {
    die(json_encode(['success' => false, 'error' => 'Not logged in']));
}

try {
    $customer = $_GET['customer'] ?? '';
    $zone = $_GET['zone'] ?? '';
    $weight = floatval($_GET['weight'] ?? 0);
    $username = $_SESSION['username'];
    $pincode = $_GET['pincode'] ?? '';

    if (empty($customer) || empty($zone) || $weight <= 0 || empty($pincode)) {
        die(json_encode(['success' => false, 'error' => 'Missing parameters']));
    }

    // Get the zone from pincode_data table
    $zone_query = "SELECT pd.zone FROM pincode_data pd WHERE pd.pincode = ?";
    $zone_stmt = $conn->prepare($zone_query);
    if (!$zone_stmt) {
        throw new Exception("Failed to prepare zone query: " . $conn->error);
    }
    
    $zone_stmt->bind_param("s", $pincode);
    if (!$zone_stmt->execute()) {
        throw new Exception("Failed to execute zone query: " . $zone_stmt->error);
    }
    
    $zone_result = $zone_stmt->get_result();
    $city = $zone; // Store the original city name

    if ($zone_row = $zone_result->fetch_assoc()) {
        // Map city zones to rate zones
        if (strtolower($city) === 'mumbai') {
            $zone = 'Intra';
        } else {
            $zone = $zone_row['zone'];
        }
    }
    $zone_stmt->close();

    // Debug log
    error_log("Surface Rate Lookup - Parameters: " . json_encode([
        'username' => $username,
        'customer' => $customer,
        'pincode' => $pincode,
        'city' => $city,
        'mapped_zone' => $zone,
        'weight' => $weight
    ]));

    // Get rates from rate_master for Surface mode
    $rate_query = "SELECT up_to_0250 as rate_1_10, 
                          up_to_0500 as rate_10_20, 
                          addl_500gm as rate_above_20, 
                          above_3kg as min_weight,
                          zone
                   FROM rate_master 
                   WHERE username = ? AND short_name = ? AND zone = ? AND mode_of_tsp = 'Surface'";
                   
    $stmt = $conn->prepare($rate_query);
    if (!$stmt) {
        throw new Exception("Failed to prepare rate query: " . $conn->error);
    }

    $stmt->bind_param("sss", $username, $customer, $zone);
    if (!$stmt->execute()) {
        throw new Exception("Failed to execute rate query: " . $stmt->error);
    }

    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $rates = $result->fetch_assoc();
        
        // Get all rates and minimum weight
        $rate_1_10 = floatval($rates['rate_1_10']);
        $rate_10_20 = floatval($rates['rate_10_20']);
        $rate_above_20 = floatval($rates['rate_above_20']);
        $min_weight = floatval($rates['min_weight']);
        
        // Calculate the rate using weight slabs
        $actual_weight = max($weight, $min_weight);
        $rounded_weight = ceil($actual_weight);
        
        // Determine which rate to use based on weight slab
        if ($rounded_weight <= 10) {
            $rate_used = $rate_1_10;
        } elseif ($rounded_weight <= 20) {
            $rate_used = $rate_10_20;
        } else {
            $rate_used = $rate_above_20;
        }
        
        $total_rate = $rounded_weight * $rate_used;
        
        $response = [
            'success' => true,
            'amount' => $total_rate,
            'actual_weight' => $actual_weight,
            'rounded_weight' => $rounded_weight,
            'rate_used' => $rate_used,
            'min_weight' => $min_weight,
            'weight_slab' => ($rounded_weight <= 10 ? '1-10' : 
                            ($rounded_weight <= 20 ? '11-20' : 'above 20')),
            'zone_used' => $zone // Add the zone being used for debugging
        ];
        
        echo json_encode($response);
    } else {
        // Log the failed query parameters
        error_log("No rates found for parameters: " . json_encode([
            'username' => $username,
            'customer' => $customer,
            'zone' => $zone,
            'mode_of_tsp' => 'Surface'
        ]));
        
        echo json_encode(['success' => false, 'error' => 'No rates found']);
    }

    $stmt->close();
} catch (Exception $e) {
    error_log("Error in get_surface_rates.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?> 