<?php
session_start();
include 'db_connect.php';

if (!isset($_SESSION['username'])) {
    header("Location: login.php");
    exit();
}

// Handle Excel export - Move this to top before any HTML output
if (isset($_GET['export_excel'])) {
    $username = $_SESSION['username'];
    $month_year = explode('-', $_GET['month'] ?? date('m-Y'));
    $month_name = date('F', mktime(0, 0, 0, $month_year[0], 1));
    $year = $month_year[1];
    
    // Set headers for CSV download
    ob_clean(); // Clean output buffer
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment;filename="Transaction_Report_' . $month_name . '_' . $year . '.csv"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Pragma: public');
    
    // Create a file pointer connected to the output stream
    $output = fopen('php://output', 'w');
    
    // Add UTF-8 BOM for proper Excel encoding
    fputs($output, "\xEF\xBB\xBF");
    
    // Construct a base query with all filters (both GET parameters and any search term)
    // SELECT ALL fields from the table
    $sql = "SELECT * FROM transactions 
            WHERE username = ?";
    $params = [$username];
    $types = "s";
    
    // Apply month filter (always active)
    $sql .= " AND MONTH(docket_date) = ? AND YEAR(docket_date) = ?";
    $params[] = $month_year[0];
    $params[] = $month_year[1];
    $types .= "ii";
    
    // Apply optional filters
    if (!empty($_GET['entry_type'])) {
        $sql .= " AND entry_type = ?";
        $params[] = $_GET['entry_type'];
        $types .= "s";
    }
    
    if (!empty($_GET['customer'])) {
        $sql .= " AND customer = ?";
        $params[] = $_GET['customer'];
        $types .= "s";
    }
    
    if (!empty($_GET['mode'])) {
        $sql .= " AND mode_of_tsp = ?";
        $params[] = $_GET['mode'];
        $types .= "s";
    }
    
    // Apply search term if present
    if (!empty($_GET['search_term'])) {
        $sql .= " AND docket_no LIKE ?";
        $params[] = '%' . $_GET['search_term'] . '%';
        $types .= "s";
    }
    
    $sql .= " ORDER BY docket_date DESC, created_at DESC";
    
    // No LIMIT clause - we want all matching records
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    // Count total records for the report
    $total_records = $result->num_rows;

    // Output headers - include ALL fields
    fputcsv($output, [
        'ID',
        'Entry Type',
        'Customer',
        'Docket No',
        'Docket Date',
        'Pincode',
        'Destination',
        'Weight (kg)',
        'Mode',
        'Waybill Value',
        'Remarks',
        'Amount (₹)',
        'Entry TS',
        'Waybill Percent',
        'ODA Charge',
        'Owner Risk',
        'Carrier Risk',
        'Payment Status',
        'Payment Received Date',
        'Waybill',
        'Mobile 1',
        'Mobile 2',
        'Username',
        'Created At'
    ]);
    
    // Output data rows - include ALL fields
    while ($row = $result->fetch_assoc()) {
        fputcsv($output, [
            $row['id'],
            $row['entry_type'],
            $row['customer'],
            $row['docket_no'],
            date('d-M-Y', strtotime($row['docket_date'])),
            $row['pincode'],
            $row['destination'],
            $row['weight'],
            $row['mode_of_tsp'],
            $row['waybill_value'],
            $row['remarks'],
            $row['amount'],
            $row['entry_ts'],
            $row['waybill_percent'],
            $row['oda_chrg'],
            $row['owner_risk'],
            $row['carrier_risk'],
            $row['payment_status'],
            !empty($row['payment_received_date']) && $row['payment_received_date'] != '0000-00-00' ? 
                date('d-M-Y', strtotime($row['payment_received_date'])) : '',
            $row['waybill'],
            $row['mobile1'],
            $row['mobile2'],
            $row['username'],
            date('d-M-Y H:i:s', strtotime($row['created_at']))
        ]);
    }
    
    fclose($output);
    $stmt->close();
    $conn->close();
    exit();
}

// Handle delete action
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['delete_id'])) {
    $id = $_POST['delete_id'];
    $username = $_SESSION['username'];
    
    $stmt = $conn->prepare("DELETE FROM transactions WHERE id = ? AND username = ?");
    $stmt->bind_param("is", $id, $username);
    
    if ($stmt->execute()) {
        $_SESSION['transaction_message'] = "Transaction deleted successfully!";
        header("Location: index.php?page=report");
        exit();
    }
    $stmt->close();
}

// Handle AJAX request
if (isset($_GET['ajax_search'])) {
    try {
        $username = $_SESSION['username'];
        $search_query = $_GET['search'] ?? '';
        
        $data = [];
        
        if (!empty($search_query)) {
            $sql = "SELECT * FROM transactions 
                    WHERE username = ? 
                    AND docket_no LIKE ?
                    ORDER BY docket_date DESC, created_at DESC";
                        
            $stmt = $conn->prepare($sql);
            $search_pattern = "%{$search_query}%";
            $stmt->bind_param("ss", $username, $search_pattern);
        } else {
            $sql = "SELECT * FROM transactions WHERE username = ? ORDER BY docket_date DESC, created_at DESC";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("s", $username);
        }
        
        if (!$stmt->execute()) {
            throw new Exception("Database error: " . $stmt->error);
        }
        
        $result = $stmt->get_result();
        
        while ($row = $result->fetch_assoc()) {
            $data[] = [
                'id' => $row['id'],
                'mode_of_tsp' => htmlspecialchars($row['mode_of_tsp']),
                'entry_type' => htmlspecialchars($row['entry_type']),
                'docket_no' => htmlspecialchars($row['docket_no']),
                'date' => date('d M Y', strtotime($row['docket_date'])),
                'customer' => htmlspecialchars($row['customer']),
                'destination' => htmlspecialchars($row['destination']),
                'weight' => htmlspecialchars($row['weight']),
                'amount' => number_format($row['amount'], 2)
            ];
        }
        
        ob_clean();
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'data' => $data,
            'total' => count($data)
        ]);
        
    } catch (Exception $e) {
        ob_clean();
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    } finally {
        if (isset($stmt)) $stmt->close();
        if (isset($conn)) $conn->close();
    }
    exit;
}

// Initial data load
$username = $_SESSION['username'];

// Get filter values
$current_month = date('m-Y');
$selected_month = $_GET['month'] ?? $current_month;

// Generate last 12 months for dropdown
$months = [];
for ($i = 0; $i < 12; $i++) {
    $date = strtotime("-$i months");
    $month_num = date('m', $date);
    $year = date('Y', $date);
    $month_name = date('M', $date);
    $months[] = [
        'value' => $month_num . '-' . $year,
        'label' => $month_name . '-' . $year
    ];
}

$selected_entry_type = $_GET['entry_type'] ?? '';
$selected_customer = $_GET['customer'] ?? '';
$selected_mode = $_GET['mode'] ?? '';

// Build the query with filters
$sql = "SELECT * FROM transactions WHERE username = ?";
$params = [$username];
$types = "s";

// Always filter by month (current month by default)
$month_year = explode('-', $selected_month);
$sql .= " AND MONTH(docket_date) = ? AND YEAR(docket_date) = ?";
$params[] = $month_year[0];
$params[] = $month_year[1];
$types .= "ii";

if (!empty($selected_entry_type)) {
    $sql .= " AND entry_type = ?";
    $params[] = $selected_entry_type;
    $types .= "s";
}

if (!empty($selected_customer)) {
    $sql .= " AND customer = ?";
    $params[] = $selected_customer;
    $types .= "s";
}

if (!empty($selected_mode)) {
    $sql .= " AND mode_of_tsp = ?";
    $params[] = $selected_mode;
    $types .= "s";
}

$sql .= " ORDER BY docket_date DESC, created_at DESC";

$stmt = $conn->prepare($sql);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$result = $stmt->get_result();

// Get unique values for filters (only for current month)
$entry_types = [];
$customers = [];
$modes = [];

$filter_stmt = $conn->prepare("SELECT DISTINCT entry_type, customer, mode_of_tsp FROM transactions 
    WHERE username = ? AND MONTH(docket_date) = ? AND YEAR(docket_date) = ?");
$filter_stmt->bind_param("sii", $username, $month_year[0], $month_year[1]);
$filter_stmt->execute();
$filter_result = $filter_stmt->get_result();

while ($row = $filter_result->fetch_assoc()) {
    if (!empty($row['entry_type'])) $entry_types[] = $row['entry_type'];
    if (!empty($row['customer'])) $customers[] = $row['customer'];
    if (!empty($row['mode_of_tsp'])) $modes[] = $row['mode_of_tsp'];
}

$entry_types = array_unique($entry_types);
$customers = array_unique($customers);
$modes = array_unique($modes);
sort($entry_types);
sort($customers);
sort($modes);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Inline Critical CSS -->
    <style>
        /* Critical styles that should load first */
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            overflow: hidden; /* Prevent scrolling during load */
        }

        /* Loading overlay - Critical */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #2196F3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        #mainContent {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease-in;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Base critical styles for content */
        .report-container {
            opacity: 0;
            transition: opacity 0.3s ease-in;
        }

        /* Success Message Styles */
        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 4px;
            background-color: #4CAF50;
            color: white;
            font-size: 16px;
            z-index: 1000;
            display: none;
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .show-message {
            display: block;
        }
    </style>

    <!-- Deferred non-critical CSS -->
    <style>
        /* Move all other styles here */
        :root {
            --primary-blue: #2196F3;
            --light-blue: #E3F2FD;
            --hover-blue: #1976D2;
            --sky-blue: #87CEEB;
            --text-dark: #2c3e50;
            --border-color: #e0e0e0;
            --background: #F8FAFC;
        }

        .report-container {
            padding: 2rem;
            margin: 0 auto;
            max-width: 1400px;
            position: relative;
            margin-left: 0px;
            margin-top: 0px;
        }

        .header-section {
            background: white;
            padding: 1.5rem 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1.5rem;
        }

        .page-title {
            color: var(--primary-blue);
            font-size: 1.75rem;
            font-weight: 600;
            margin: 0;
        }

        .search-section {
            flex: 1;
            max-width: 500px;
            min-width: 280px;
        }

        .search-container {
            position: relative;
            width: 100%;
        }

        .search-box {
            width: 100%;
            padding: 0.75rem 3.5rem 0.75rem 1rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: var(--background);
        }

        .search-box:focus {
            outline: none;
            border-color: var(--primary-blue);
            background: white;
            box-shadow: 0 0 0 4px rgba(33, 150, 243, 0.1);
        }

        .clear-search {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            border: none;
            background: var(--light-blue);
            color: var(--primary-blue);
            cursor: pointer;
            padding: 0.4rem 0.8rem;
            display: none;
            font-size: 0.85rem;
            border-radius: 6px;
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .clear-search:hover {
            background: var(--primary-blue);
            color: white;
        }

        .clear-search:active {
            transform: translateY(-50%) scale(0.95);
        }

        .total-badge {
            background: var(--light-blue);
            color: var(--primary-blue);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 500;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .data-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            table-layout: auto;
        }

        .table thead th {
            background: var(--primary-blue);
            color: white;
            padding: 0.75rem;
            font-weight: 500;
            text-align: left;
            font-size: 0.9rem;
            white-space: nowrap;
            cursor: pointer;
            user-select: none;
            position: relative;
        }

        .table tbody td {
            padding: 0.5rem 0.75rem;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-dark);
            font-size: 0.9rem;
            line-height: 1.2;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Customer column specific styles */
        .table td:nth-child(2) {
            white-space: normal;
            word-break: break-word;
            min-width: 120px;
            max-width: 200px;
        }

        .table thead th:hover {
            background: var(--hover-blue);
        }

        .table thead th::after {
            content: '↕';
            position: absolute;
            right: 8px;
            opacity: 0.5;
        }

        .table thead th.sort-asc::after {
            content: '↑';
            opacity: 1;
        }

        .table thead th.sort-desc::after {
            content: '↓';
            opacity: 1;
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        .table tbody tr:hover {
            background: var(--background);
        }

        .action-btns {
            display: flex;
            gap: 0.5rem;
            white-space: nowrap;
        }

        .btn-edit, .btn-delete {
            padding: 0.4rem 0.75rem;
            font-size: 0.85rem;
        }

        .btn-edit {
            background: var(--light-blue);
            color: var(--primary-blue);
            border: 1px solid transparent;
        }

        .btn-edit:hover {
            background: var(--primary-blue);
            color: white;
        }

        .btn-delete {
            background: #FEE2E2;
            color: #DC2626;
            border: 1px solid transparent;
        }

        .btn-delete:hover {
            background: #DC2626;
            color: white;
        }

        .loading-indicator {
            display: none;
            width: 24px;
            height: 24px;
            border: 2px solid var(--light-blue);
            border-radius: 50%;
            border-top: 2px solid var(--primary-blue);
            animation: spin 0.8s linear infinite;
            position: absolute;
            right: -40px;
            top: 50%;
            transform: translateY(-50%);
        }

        @media (max-width: 1024px) {
            .report-container {
                padding: 1rem;
            }

            .header-section {
                padding: 1rem;
            }
        }

        @media (max-width: 768px) {
            .header-section {
                flex-direction: column;
                align-items: stretch;
            }

            .search-section {
                order: 2;
            }

            .total-badge {
                align-self: flex-start;
            }

            .table thead {
                display: none;
            }

            .table tbody td {
                display: block;
                padding: 0.5rem 1rem;
                text-align: right;
                border: none;
            }

            .table tbody tr {
                display: block;
                border-bottom: 1px solid var(--border-color);
                padding: 0.5rem 0;
            }

            .table td::before {
                content: attr(data-label);
                float: left;
                font-weight: 500;
                color: var(--primary-blue);
            }

            .action-btns {
                justify-content: flex-end;
                margin-top: 0.5rem;
            }
        }

        /* Filter Styles */
        .filters-section {
            width: 100%;
            margin-bottom: 1.5rem;
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .filter-group label {
            font-size: 0.9rem;
            color: var(--text-dark);
            font-weight: 500;
        }

        .filter-input {
            padding: 0.75rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: var(--background);
        }

        .filter-input:focus {
            outline: none;
            border-color: var(--primary-blue);
            background: white;
            box-shadow: 0 0 0 4px rgba(33, 150, 243, 0.1);
        }

        .filter-buttons {
            display: flex;
            gap: 0.5rem;
            align-items: flex-end;
        }

        .btn-apply, .btn-reset {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            text-align: center;
        }

        .btn-apply {
            background: var(--primary-blue);
            color: white;
            border: none;
        }

        .btn-apply:hover {
            background: var(--hover-blue);
        }

        .btn-reset {
            background: var(--light-blue);
            color: var(--primary-blue);
            border: 1px solid transparent;
        }

        .btn-reset:hover {
            background: var(--primary-blue);
            color: white;
        }

        .btn-excel {
            background: #217346;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            text-align: center;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-excel:hover {
            background: #1a5c38;
            color: white;
        }

        @media (max-width: 768px) {
            .filters-grid {
                grid-template-columns: 1fr;
            }
            
            .filter-buttons {
                flex-direction: column;
            }
            
            .btn-apply, .btn-reset {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Main content container -->
    <div id="mainContent">

<!-- Report Component -->
<div class="report-container">
    <?php if (isset($_SESSION['transaction_message'])): ?>
        <div class="message" id="successMessage">
            <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($_SESSION['transaction_message']); ?>
        </div>
        <?php unset($_SESSION['transaction_message']); ?>
    <?php endif; ?>
    
    <div class="header-section">
        <h1 class="page-title">Transaction Report</h1>
        <div class="filters-section">
            <form id="filterForm" method="GET" action="index.php" class="filters-grid">
                <input type="hidden" name="page" value="report">
                
                <div class="filter-group">
                    <label for="month">Month</label>
                    <select id="month" name="month" class="filter-input">
                        <?php foreach ($months as $month): ?>
                            <option value="<?php echo $month['value']; ?>" <?php echo $selected_month === $month['value'] ? 'selected' : ''; ?>>
                                <?php echo $month['label']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="entry_type">Entry Type</label>
                    <select id="entry_type" name="entry_type" class="filter-input">
                        <option value="">All</option>
                        <?php foreach ($entry_types as $type): ?>
                            <option value="<?php echo htmlspecialchars($type); ?>" <?php echo $selected_entry_type === $type ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($type); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="customer">Customer</label>
                    <select id="customer" name="customer" class="filter-input">
                        <option value="">All</option>
                        <?php foreach ($customers as $customer): ?>
                            <option value="<?php echo htmlspecialchars($customer); ?>" <?php echo $selected_customer === $customer ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($customer); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="mode">Mode</label>
                    <select id="mode" name="mode" class="filter-input">
                        <option value="">All</option>
                        <?php foreach ($modes as $mode): ?>
                            <option value="<?php echo htmlspecialchars($mode); ?>" <?php echo $selected_mode === $mode ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($mode); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="filter-group filter-buttons">
                    <button type="submit" class="btn-apply">Apply Filters</button>
                    <a href="report.php" class="btn-reset">Reset</a>
                    <a href="report.php?export_excel=1<?php
                        echo '&month=' . urlencode($selected_month);
                        if (!empty($selected_entry_type)) echo '&entry_type=' . urlencode($selected_entry_type);
                        if (!empty($selected_customer)) echo '&customer=' . urlencode($selected_customer);
                        if (!empty($selected_mode)) echo '&mode=' . urlencode($selected_mode);
                    ?>" class="btn-excel" id="exportExcel">
                        <i class="fas fa-file-excel"></i>
                        Download Excel
                    </a>
                </div>
            </form>
        </div>
        <div class="search-section">
            <div class="search-container">
                <input type="text" 
                       id="searchBox" 
                       class="search-box" 
                       placeholder="Search by docket number..."
                       autocomplete="off">
                <button type="button" id="clearSearch" class="clear-search">
                    Clear
                </button>
                <div id="loadingIndicator" class="loading-indicator"></div>
            </div>
        </div>
        <div class="total-badge">
            <i class="fas fa-file-alt"></i>
            <span>Total Records: <span id="recordCount"><?php echo $result->num_rows; ?></span></span>
        </div>
    </div>

    <div class="data-table">
        <table class="table">
            <thead>
                <tr><th>Entry Type</th>
                    <th>Customer</th>
                    <th>Docket No</th>
                    <th>Date</th>
                    <th>Mode</th>
                    <th>Destination</th>
                    <th>Weight</th>
                    <th>Amount</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody id="transactionTableBody">
                <?php while ($row = $result->fetch_assoc()): ?>
                <tr>
                    <td data-label="entry type"><?php echo htmlspecialchars($row['entry_type']); ?></td>
                    <td data-label="customer"><?php echo htmlspecialchars($row['customer']); ?></td>
                    <td data-label="docket no"><?php echo htmlspecialchars($row['docket_no']); ?></td>
                    <td data-label="date"><?php echo date('d-M-Y', strtotime($row['docket_date'])); ?></td>
                    <td data-label="mode"><?php echo htmlspecialchars($row['mode_of_tsp']); ?> </td>
                    <td data-label="destination"><?php echo htmlspecialchars($row['destination']); ?></td>
                    <td data-label="weight"><?php echo htmlspecialchars($row['weight']); ?> kg</td>
                    <td data-label="amount">₹<?php echo number_format($row['amount'], 2); ?></td>
                    <td data-label="actions">
                        <div class="action-btns">
                            <a href="edit_transaction.php?id=<?php echo $row['id']; ?>" class="btn-edit">
                                <i class="fas fa-edit"></i>
                                Edit
                            </a>
                            <form method="POST" action="report.php" style="display:inline;">
                                <input type="hidden" name="delete_id" value="<?php echo $row['id']; ?>">
                                <button type="submit" class="btn-delete" onclick="return confirm('Delete this transaction?')">
                                    <i class="fas fa-trash"></i>
                                    Delete
                                </button>
                            </form>
                        </div>
                    </td>
                </tr>
                <?php endwhile; ?>
            </tbody>
        </table>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(document).ready(function() {
    // Add success message handling
    const successMessage = document.getElementById('successMessage');
    if (successMessage) {
        successMessage.classList.add('show-message');
        setTimeout(() => {
            successMessage.classList.remove('show-message');
            setTimeout(() => {
                successMessage.style.display = 'none';
            }, 500);
        }, 3000);
    }

    const loadingOverlay = document.getElementById('loadingOverlay');
    const mainContent = document.getElementById('mainContent');
    const reportContainer = document.querySelector('.report-container');
    
    // Ensure the loading overlay is visible initially
    loadingOverlay.style.visibility = 'visible';
    loadingOverlay.style.opacity = '1';
    
    // Hide main content initially
    mainContent.style.visibility = 'hidden';
    mainContent.style.opacity = '0';
    
    // Function to show content
    function showContent() {
        // First hide the loading overlay
        loadingOverlay.style.opacity = '0';
        
        // After a brief delay, show the main content
        setTimeout(() => {
            // Hide loading overlay completely
            loadingOverlay.style.visibility = 'hidden';
            
            // Show main content
            mainContent.style.visibility = 'visible';
            mainContent.style.opacity = '1';
            document.body.style.overflow = 'auto'; // Re-enable scrolling
            
            // Show report container with a slight delay for smooth transition
            setTimeout(() => {
                reportContainer.style.opacity = '1';
            }, 50);
        }, 300);
    }
    
    // Wait for everything to load
    if (document.readyState === 'complete') {
        showContent();
    } else {
        window.addEventListener('load', showContent);
    }

    let searchTimer;
    const searchBox = $('#searchBox');
    const clearButton = $('#clearSearch');
    const loadingIndicator = $('#loadingIndicator');
    const tbody = $('#transactionTableBody');
    let currentSort = { column: null, direction: 'asc' };
    
    // Add click handlers to table headers
    $('.table thead th').each(function(index) {
        $(this).on('click', function() {
            const column = $(this).text().toLowerCase().trim();
            console.log('Sorting by:', column); // Debug log
            
            // Remove sort classes from all headers
            $('.table thead th').removeClass('sort-asc sort-desc');
            
            // Update sort direction
            if (currentSort.column === column) {
                currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
            } else {
                currentSort.column = column;
                currentSort.direction = 'asc';
            }
            
            // Add sort class to current header
            $(this).addClass(`sort-${currentSort.direction}`);
            
            // Sort the table
            sortTable(column, currentSort.direction);
        });
    });
    
    function sortTable(column, direction) {
        const rows = tbody.find('tr').toArray();
        const multiplier = direction === 'asc' ? 1 : -1;
        
        rows.sort((a, b) => {
            let aValue = $(a).find(`td[data-label="${column}"]`).text().trim();
            let bValue = $(b).find(`td[data-label="${column}"]`).text().trim();
            
            // Remove currency symbol and commas for amount
            if (column === 'amount') {
                aValue = parseFloat(aValue.replace(/[₹,]/g, ''));
                bValue = parseFloat(bValue.replace(/[₹,]/g, ''));
            }
            // Remove 'kg' and convert to number for weight
            else if (column === 'weight') {
                aValue = parseFloat(aValue.replace(' kg', ''));
                bValue = parseFloat(bValue.replace(' kg', ''));
            }
            // Parse date
            else if (column === 'date') {
                aValue = new Date(aValue.split('-').reverse().join('-'));
                bValue = new Date(bValue.split('-').reverse().join('-'));
            }
            // Default string comparison
            else {
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }
            
            if (aValue < bValue) return -1 * multiplier;
            if (aValue > bValue) return 1 * multiplier;
            return 0;
        });
        
        tbody.empty().append(rows);
    }
    
    function showError(message) {
        tbody.empty();
        tbody.append(`
            <tr>
                <td colspan="8" style="text-align: center; padding: 2rem;">
                    <div style="color: #DC2626;">${message}</div>
                </td>
            </tr>
        `);
    }

    // Search functionality
    searchBox.on('input', function() {
        const searchValue = $(this).val().trim();
        clearButton.toggle(searchValue.length > 0);
        
        // Update the Excel download link to include the search term
        updateExcelLink(searchValue);
        
        clearTimeout(searchTimer);
        
        if (searchValue.length > 0) {
            loadingIndicator.show();
            
            searchTimer = setTimeout(() => {
                $.ajax({
                    url: 'index.php?page=report&ajax_search=1',
                    data: { search: searchValue },
                    method: 'GET',
                    success: function(response) {
                        if (response.success) {
                            tbody.empty();
                            $('#recordCount').text(response.total);
                            
                            if (response.data.length === 0) {
                                showError('No matching records found');
                                return;
                            }
                            
                            response.data.forEach(row => {
                                tbody.append(`
                                    <tr>
                                        <td data-label="entry type">${row.entry_type}</td>
                                        <td data-label="customer">${row.customer}</td>
                                        <td data-label="docket no">${row.docket_no}</td>
                                        <td data-label="date">${row.date}</td>
                                        <td data-label="mode">${row.mode_of_tsp}</td>
                                        <td data-label="destination">${row.destination}</td>
                                        <td data-label="weight">${row.weight} kg</td>
                                        <td data-label="amount">₹${row.amount}</td>
                                        <td data-label="actions">
                                            <div class="action-btns">
                                                <a href="index.php?page=edit_transaction&id=${row.id}" class="btn-edit">
                                                    <i class="fas fa-edit"></i>
                                                    Edit
                                                </a>
                                                <form method="POST" action="index.php?page=report" style="display:inline;">
                                                    <input type="hidden" name="delete_id" value="${row.id}">
                                                    <button type="submit" class="btn-delete" onclick="return confirm('Delete this transaction?')">
                                                        <i class="fas fa-trash"></i>
                                                        Delete
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                `);
                            });
                        } else {
                            showError('Error loading data');
                        }
                    },
                    error: function() {
                        showError('Error loading data');
                    },
                    complete: function() {
                        loadingIndicator.hide();
                    }
                });
            }, 300);
        } else {
            location.reload();
        }
    });

    // Update Excel link with search term
    function updateExcelLink(searchTerm) {
        const exportLink = $('#exportExcel');
        let href = exportLink.attr('href');
        
        // Remove any existing search term
        href = href.replace(/&search_term=[^&]*/, '');
        
        // Add new search term if present
        if (searchTerm && searchTerm.length > 0) {
            href += '&search_term=' + encodeURIComponent(searchTerm);
        }
        
        exportLink.attr('href', href);
    }

    // Clear search
    clearButton.on('click', function() {
        searchBox.val('').trigger('input');
    });
});
</script>
</body>
</html>