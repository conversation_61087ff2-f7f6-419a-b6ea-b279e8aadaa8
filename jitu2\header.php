<?php
ob_start(); // Start output buffering
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include database connection if not already included
if (!isset($conn)) {
    include_once 'db_connect.php';
}

// Store alert message in a variable instead of outputting directly
$alertHtml = '';
if (isset($_SESSION['alert_message'])) {
    $alertHtml = '
    <div class="alert-message alert-' . ($_SESSION['alert_type'] ?? 'success') . '" id="alertMessage">
        <i class="fas ' . ($_SESSION['alert_type'] === 'error' ? 'fa-exclamation-circle' : 'fa-check-circle') . '"></i>
        ' . htmlspecialchars($_SESSION['alert_message']) . '
    </div>
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            const message = document.getElementById("alertMessage");
            message.classList.add("show-alert");
            setTimeout(() => {
                message.classList.remove("show-alert");
            }, 3000);
        });
    </script>';
    
    unset($_SESSION['alert_message']);
    unset($_SESSION['alert_type']);
}

// Fetch user data if logged in
$user = ['owner_name' => 'Owner', 'profile_picture' => ''];
if (isset($_SESSION['username'])) {
    try {
        $username = $_SESSION['username'];
        $stmt = $conn->prepare("SELECT owner_name, profile_picture FROM users WHERE username = ?");
        $stmt->bind_param("s", $username);
        $stmt->execute();
        $result = $stmt->get_result();
        $userData = $result->fetch_assoc();
        if ($userData) {
            $user = $userData;
        }
    } catch (Exception $e) {
        // Keep default values if query fails
    }
}
?><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Astra Logistics Manager</title>
    <link rel="icon" type="image/x-icon" href="images/favicon.ico" />  
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="css/header.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        :root {
            --primary-color: #4f46e5;
            --secondary-color: #7c3aed;
            --accent-color: #06b6d4;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --background-color: #ffffff;
            --text-color: #1f2937;
            --text-muted: #64748b;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --header-height: 70px;
            --glass-bg: rgba(255, 255, 255, 0.98);
            --glass-border: rgba(255, 255, 255, 0.2);
            --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            --gradient-accent: linear-gradient(135deg, var(--accent-color), var(--primary-color));
            --gradient-success: linear-gradient(135deg, var(--success-color), #059669);
            --gradient-warning: linear-gradient(135deg, var(--warning-color), #d97706);
            --gradient-danger: linear-gradient(135deg, var(--danger-color), #dc2626);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', sans-serif;
            margin-top: var(--header-height);
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        .navbar {
            background: var(--glass-bg);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border-bottom: 1px solid var(--glass-border);
            color: var(--text-color);
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: fixed;
            width: 100%;
            top: 0;
            height: var(--header-height);
            z-index: 1000;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        }

        .navbar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, 
                var(--primary-color) 0%,
                var(--secondary-color) 25%,
                var(--accent-color) 50%,
                var(--success-color) 75%,
                var(--warning-color) 100%
            );
            opacity: 0.8;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-color);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: all 0.3s ease;
            position: relative;
            padding: 0.5rem 0;
        }

        .navbar-brand .brand-text {
            display: flex;
            flex-direction: column;
            line-height: 1.2;
            position: relative;
        }

        .navbar-brand .brand-name {
            font-size: 1.4rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            letter-spacing: -0.02em;
            filter: drop-shadow(0 2px 4px rgba(79, 70, 229, 0.2));
            position: relative;
            transform-style: preserve-3d;
            perspective: 500px;
        }

        .navbar-brand .brand-name::after {
            content: 'Astra Logistics Manager';
            position: absolute;
            left: 0;
            top: 0;
            background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            opacity: 0;
            transform: translateZ(-10px);
            transition: all 0.3s ease;
        }

        .navbar-brand:hover .brand-name::after {
            opacity: 1;
            transform: translateZ(0);
        }

        .navbar-brand .brand-tagline {
            font-size: 0.8rem;
            font-weight: 500;
            color: var(--text-muted);
            letter-spacing: 0.05em;
            text-transform: uppercase;
            opacity: 0.9;
            margin-top: 0.2rem;
            position: relative;
            padding-left: 1.5rem;
            transition: all 0.3s ease;
        }

        .navbar-brand .brand-tagline::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            width: 1rem;
            height: 2px;
            background: var(--gradient-primary);
            transform: translateY(-50%);
            border-radius: 2px;
            transition: all 0.3s ease;
        }

        .navbar-brand:hover .brand-tagline {
            padding-left: 2rem;
            color: var(--primary-color);
        }

        .navbar-brand:hover .brand-tagline::before {
            width: 1.5rem;
        }

        .navbar-brand i {
            font-size: 2rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            filter: drop-shadow(0 2px 4px rgba(79, 70, 229, 0.2));
            transition: all 0.3s ease;
        }

        .navbar-brand:hover i {
            transform: scale(1.1) rotate(-5deg);
        }

        .navbar-brand::after {
            display: none;
        }

        .navbar-brand:hover {
            transform: translateY(-2px);
        }

        .user-profile {
            position: relative;
            margin-left: auto;
        }

        .user-icon {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 12px;
            transition: all 0.3s ease;
            background: rgba(79, 70, 229, 0.1);
            border: 1px solid rgba(79, 70, 229, 0.2);
        }

        .user-icon:hover {
            background: rgba(79, 70, 229, 0.15);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.1);
        }

        .user-icon img.profile-pic {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid transparent;
            background: var(--gradient-primary);
            padding: 2px;
            box-shadow: 0 2px 8px rgba(79, 70, 229, 0.2);
        }

        .user-icon .user-info {
            display: flex;
            flex-direction: column;
        }

        .user-icon .owner-name {
            font-weight: 600;
            color: var(--text-color);
            font-size: 0.9rem;
        }

        .user-icon .username {
            font-size: 0.75rem;
            color: var(--text-muted);
        }

        .profile-dropdown {
            position: absolute;
            top: calc(100% + 0.5rem);
            right: 0;
            background: var(--glass-bg);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            display: none;
            min-width: 220px;
            overflow: hidden;
            transform-origin: top right;
            animation: dropdownFade 0.2s ease;
        }

        @keyframes dropdownFade {
            from {
                opacity: 0;
                transform: scale(0.95) translateY(-10px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .user-profile:hover .profile-dropdown {
            display: block;
        }

        .profile-dropdown a {
            display: flex;
            align-items: center;
            padding: 0.875rem 1rem;
            color: var(--text-color);
            text-decoration: none;
            transition: all 0.2s ease;
            gap: 0.75rem;
            border-bottom: 1px solid var(--glass-border);
            position: relative;
            overflow: hidden;
            font-size: 0.9rem;
        }

        .profile-dropdown a::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 3px;
            background: var(--gradient-primary);
            transform: scaleY(0);
            transition: transform 0.3s ease;
        }

        .profile-dropdown a:hover::before {
            transform: scaleY(1);
        }

        .profile-dropdown a:last-child {
            border-bottom: none;
        }

        .profile-dropdown a:hover {
            background: rgba(79, 70, 229, 0.1);
            color: var(--primary-color);
        }

        .profile-dropdown a i {
            font-size: 1rem;
            color: var(--primary-color);
            transition: transform 0.2s ease;
        }

        .profile-dropdown a:hover i {
            transform: translateX(4px);
        }

        .alert-message {
            position: fixed;
            top: calc(var(--header-height) + 1rem);
            right: 1rem;
            padding: 1rem 1.5rem;
            border-radius: 12px;
            color: white;
            font-size: 0.9rem;
            z-index: 1000;
            display: none;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            max-width: 400px;
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .alert-success {
            background: var(--gradient-success);
        }

        .alert-error {
            background: var(--gradient-danger);
        }

        @media (max-width: 768px) {
            .navbar {
                padding: 0 1rem;
            }

            .navbar-brand i {
                font-size: 1.8rem;
            }

            .navbar-brand .brand-text {
                display: none;
            }

            .user-icon .user-info {
                display: none;
            }

            .profile-dropdown {
                right: -1rem;
            }
        }
    </style>
</head>
<body>
<?php echo $alertHtml; ?>

<header class="navbar">
    <a href="dashboard.php" class="navbar-brand">
        <i class="fas fa-truck-loading"></i>
        <div class="brand-text">
            <span class="brand-name">Astra Logistics Manager</span>
            <span class="brand-tagline">Your Partner in Logistics Success</span>
        </div>
    </a>
    
    <?php if (isset($_SESSION['username'])): ?>
        <div class="user-profile">
            <div class="user-icon" id="profileToggle">
                <?php if (!empty($user['profile_picture'])): ?>
                    <img src="<?php echo htmlspecialchars($user['profile_picture']); ?>" alt="Profile Picture" class="profile-pic">
                <?php else: ?>
                    <i class="fas fa-user-circle fa-2x" style="color: var(--primary-color);"></i>
                <?php endif; ?>
                <div class="user-info">
                    <span class="owner-name"><?php echo htmlspecialchars($user['owner_name']); ?></span>
                    <span class="username"><?php echo htmlspecialchars($_SESSION['username']); ?></span>
                </div>
            </div>
            <div class="profile-dropdown" id="profileDropdown">
                <a href="dashboard.php?page=profile">
                    <i class="fas fa-user-edit"></i>
                    Edit Profile
                </a>
                <a href="dashboard.php?page=settings">
                    <i class="fas fa-cog"></i>
                    Settings
                </a>
                <a href="logout.php">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </a>
            </div>
        </div>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const profileToggle = document.getElementById('profileToggle');
                const profileDropdown = document.getElementById('profileDropdown');
                const userProfile = document.querySelector('.user-profile');
                let timeoutId;

                // Toggle dropdown on click
                profileToggle.addEventListener('click', function(e) {
                    e.stopPropagation();
                    if (profileDropdown.style.display === 'block') {
                        profileDropdown.style.display = 'none';
                    } else {
                        profileDropdown.style.display = 'block';
                    }
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!userProfile.contains(e.target)) {
                        profileDropdown.style.display = 'none';
                    }
                });

                // Handle hover behavior
                userProfile.addEventListener('mouseenter', function() {
                    clearTimeout(timeoutId);
                    profileDropdown.style.display = 'block';
                });

                userProfile.addEventListener('mouseleave', function() {
                    timeoutId = setTimeout(() => {
                        if (!profileDropdown.matches(':hover')) {
                            profileDropdown.style.display = 'none';
                        }
                    }, 800); // Increased delay to 800ms for better usability
                });

                profileDropdown.addEventListener('mouseleave', function() {
                    timeoutId = setTimeout(() => {
                        if (!userProfile.matches(':hover')) {
                            profileDropdown.style.display = 'none';
                        }
                    }, 800);
                });

                profileDropdown.addEventListener('mouseenter', function() {
                    clearTimeout(timeoutId);
                });
            });
        </script>
    <?php else: ?>
        <ul class="navbar-nav">
            <li><a href="login.php">Login</a></li>
            <li><a href="register.php">Register</a></li>
        </ul>
    <?php endif; ?>
</header>
</body>
</html>