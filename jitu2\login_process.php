<?php

session_start();

header('Content-Type: application/json');



// Database Connection for InfinityFree

$servername = "localhost";
$username = "u111133901_yashent";
$password = "Yash@400709";
$database = "u111133901_yashent";



$response = array();



try {

    $conn = new mysqli($servername, $username, $password, $database);



    // Check connection

    if ($conn->connect_error) {

        throw new Exception("Connection failed: " . $conn->connect_error);

    }



    // Get JSON data from the request

    $json_data = file_get_contents('php://input');

    $data = json_decode($json_data, true);



    if (!$data) {

        throw new Exception("Invalid request data");

    }



    $username = trim($data['username']);

    $password = $data['password'];

    $remember = isset($data['remember']) ? $data['remember'] : false;



    // Fetch user from database using username

    $stmt = $conn->prepare("SELECT id, username, password_hash FROM users WHERE username = ?");

    $stmt->bind_param("s", $username);

    $stmt->execute();

    $stmt->store_result();

    

    if ($stmt->num_rows > 0) {

        $stmt->bind_result($user_id, $db_username, $password_hash);

        $stmt->fetch();



        // Verify password

        if (password_verify($password, $password_hash)) {

            $_SESSION['user_id'] = $user_id;

            $_SESSION['username'] = $db_username;



            // Handle remember me functionality

            if ($remember) {

                $token = bin2hex(random_bytes(32));

                setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30 days

                

                // Store token in database (you may want to create a remember_tokens table)

                // This is a basic example - consider implementing proper token storage

                $stmt = $conn->prepare("UPDATE users SET remember_token = ? WHERE id = ?");

                $stmt->bind_param("si", $token, $user_id);

                $stmt->execute();

            }



            $response['success'] = true;

            $response['message'] = "Login successful";

            $response['redirect'] = "dashboard.php";

        } else {

            throw new Exception("Invalid username or password");

        }

    } else {

        throw new Exception("Invalid username or password");

    }



    $stmt->close();

    $conn->close();



} catch (Exception $e) {

    $response['success'] = false;

    $response['message'] = $e->getMessage();

}



echo json_encode($response);

?> 